<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>集成版搜索框（含沟通记录时间选择）</title>
  <style>
      .months-container {
          display: flex;
          align-items: center;
          position: relative;
          width: 100%;
          height: fit-content;
          border-radius: 12px;
          overflow: visible;
      }
      .months-viewport {
          flex: 1;
          height: 100%;
          overflow: hidden;
          position: relative;

      }
      .months-track {
          display: flex;
          height: 100%;
          transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          align-items: center;
          gap: 12px;

      }
      .months-track .month-card:first-child {
  /* margin-left: 25px; */
}
.dropdown-content .month-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 16px;
    background: white;
    border: 1px solid #dddddd;
    border-radius: 20px;
    cursor: pointer;
    transition: border-color 0.25s ease, box-shadow 0.25s ease, transform 0.1s ease;
    min-width: 120px;
    height: 136px;
    justify-content: center;
}
.dropdown-content .month-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 12px;
    opacity: 0.7;
}
.dropdown-content .month-card.active .month-icon {
    opacity: 1;
}
      .month-name {
          font-size: 14px;
          font-weight: 600;
          color: #222;
          margin-bottom: 2px;
      }
      .month-year {
          font-size: 12px;
          color: #717171;
      }
      .nav-button {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 32px;
          height: 32px;
          border: none;
          background: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
          border: 1px solid rgb(0 0 0/0.08);
          transition: all 0.2s ease;
          z-index: 100;
      }
      .nav-button:hover {
          background: #f8f9fa;
      }
      .nav-button:disabled {
          opacity: 0;
          cursor: not-allowed;
          transform: translateY(-50%);
      }
      .nav-button.prev {
          left: -5px;
      }
      .nav-button.next {
          right: -5px;
      }
      * { margin: 0; padding: 0; box-sizing: border-box; }
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #fff; min-height: 100vh; padding: 100px 20px; }
      .search-container { max-width: 850px; margin: 0 auto; position: relative; }
      .search-bar { background: white; border-radius: 40px; box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05); display: flex; align-items: center; transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); border: 1px solid #ddd; position: relative; z-index: 1001; overflow: visible; }
      .search-bar.expanded { box-shadow: none; background: #ebebeb; }
.active-indicator { 
    position: absolute; 
    background: white; 
    border-radius: 40px; 
    box-shadow: rgba(0, 0, 0, 0.1) 0px 3px 12px 0px, rgba(0, 0, 0, 0.08) 0px 1px 2px 0px;
    z-index: 1; opacity: 0; 
    transform: scale(0.5);
    transition: transform .3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    
}
      .active-indicator.show { opacity: 1; transform: scale(1); }
      .search-item { flex: 1; padding: 16px 32px; line-height: 32px; cursor: pointer; position: relative; z-index: 2; color: #555; transition: all 0.3s ease; border-radius: 40px; }
      .search-item[data-type="guests"] { padding-right: 64px; }
      .search-item:not(.active):hover { background: rgba(0,0,0,0.05); }
      .search-item.active { color: #000; background: transparent; }
      .search-bar.hovering-item .search-separator { opacity: 0; }
      .search-placeholder { font-size: 14px; color: #717171; font-weight: 400; }
      .search-separator { width: 1px; height: 32px; background-color: #ddd; transition: opacity 0.3s ease; opacity: 1; }
      .search-bar.expanded .search-separator { opacity: 0; }
      .search-item.active .search-placeholder { color: #000; }
      .search-button { background: linear-gradient(135deg, #ff385c, #e31c5f); border: none; border-radius: 50%; width: 48px; height: 48px; margin: 7px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease, width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), border-radius 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); box-shadow: 0 2px 8px rgba(255, 56, 92, 0.3); position: absolute; right: 0; z-index: 1002; padding: 0 16px; overflow: hidden; }
      .search-button:hover { background: linear-gradient(135deg, #e31c5f, #d1185a); transform: scale(1.05); box-shadow: 0 4px 12px rgba(255, 56, 92, 0.4); }
      .search-button.expanded { width: 80px; border-radius: 24px; justify-content: flex-start; padding-left: 12px; }
      .search-icon { width: 16px; height: 16px; fill: white; transition: margin-right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
      .search-button.expanded .search-icon { margin-right: 8px; }
      .search-button-text { color: white; font-size: 16px; font-weight: 500; white-space: nowrap; opacity: 0; transform: translateX(10px); transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
      .search-button.expanded .search-button-text { opacity: 1; transform: translateX(0); }
      .clear-button { position: absolute; right: 92px; top: 50%; transform: translateY(-50%) scale(0); width: 24px; height: 24px; border: none; background: rgba(0,0,0,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1), opacity 0.3s cubic-bezier(0.175, 0.885, 0.32, 1), background 0.2s ease; z-index: 3; opacity: 0; }
      .clear-button:hover { background: rgba(0,0,0,0.2); }
      .clear-button svg { width: 12px; height: 12px; stroke: white; stroke-width: 2; }
      .search-input { border: none; outline: none; background: transparent; font-size: 14px; color: #000; font-weight: 400; width: 100%; font-family: inherit; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
      .search-input::placeholder { color: #717171; font-weight: 400; }
      .search-input:focus::placeholder { color: #999; }
      .dropdown-container { 
        position: absolute; 
        top: calc(100% + 12px); 
        background: white; border-radius: 32px; 
        filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.14)); 
        opacity: 0; 
        visibility: hidden; 
        transform: scale(0.5); 
        z-index: 1000; 
        overflow: hidden; 
        border: 1px solid rgba(0,0,0,0.08); 
        transform-origin: top center; }
      .dropdown-container:not(.show) {
          visibility: hidden;
      }

      .dropdown-container.show {
          opacity: 1;
          visibility: visible;
          transform: translateY(0) scale(1);
    transition-delay: 0s;
      }
      .dropdown-content { padding: 42px 10px 30px ; min-height: 200px; }
      .dropdown-section { margin-bottom: 32px; }
      .dropdown-section:last-child { margin-bottom: 0; }
      .dropdown-title { margin: 0 0 2px 20px; padding: 0 0 0 10px; font-size: 12px; font-weight: 400; color: #222; margin-bottom: 10px; }
      .location-list { max-height: 420px; overflow-y: auto; padding-right: 8px; scrollbar-width: thin; scrollbar-color: #c1c1c1 #f1f1f1; }
      .location-list::-webkit-scrollbar { width: 6px; }
      .location-list::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 3px; }
      .location-list::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 3px; }
      .location-list::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }
      .location-item { padding: 16px 20px; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; margin-bottom: 8px; border: 1px solid transparent; display: flex; align-items: center; gap: 16px; }
      .location-item:hover {
        background: linear-gradient(135deg, #f8f9ff, #f0f2ff);
        border-color: rgba(103, 126, 234, 0.2);
        transform: translateX(4px);
    }
      .location-icon { width: 56px; height: 56px; background: #f7f7f7; border-radius: 12px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; }
      .location-icon svg { width: 32px; height: 32px; stroke: #6a6a6a; }
      .location-content { flex: 1; }
      .location-name { font-size: 15px; font-weight: 500; color: #222; margin-bottom: 2px; }
      .location-desc { font-size: 13px; color: #717171; }
      .location-item:active { transform: translateX(4px) scale(0.98); }
      @media (max-width: 768px) { .search-container { max-width: 100%; margin: 0 16px; } .search-item { padding: 16px 32px; } .search-item[data-type="guests"] { padding-right: 64px; } .dropdown-content { padding: 24px; } }

      /* 融入 time-picker.css 样式，冲突时保留原有样式 */
      .dropdown-content .container {
          max-width: 780px;
          margin: 0 auto;
      }

      .dropdown-content .dropdown {
          background: white;
          border-radius: 16px;
          box-shadow: 0 6px 16px rgba(0,0,0,0.12);
          overflow: visible;
          border: 1px solid #dddddd;
      }

      .dropdown-content .dropdown-header {
          display: flex;
          justify-content: center;
      }

      .dropdown-content .tabs-container {
          display: flex;
          background: #ebebeb;
          border-radius: 32px;
          padding: 4px;
          gap: 5px;
      }

      .dropdown-content .tab {
          padding: 7px 22px;
          border-radius: 28px;
          cursor: pointer;
          font-weight: 500;
          font-size: 14px;
          color: #666666;
          background: transparent;
          border: none;
          transition: all 0.2s ease;
          font-family: inherit;
          white-space: nowrap;
      }

      .dropdown-content .tab:hover {
          background: rgba(255, 255, 255, 0.5);
          color: #222;
      }

      .dropdown-content .tab.active {
          background: white;
          border: 1px solid #ddd;
          color: #222222;
      }

      .dropdown-content .tab.active:hover {
          background: white;
          color: #222222;
      }

      .dropdown-content .projects-view h3 {
          font-size: 18px;
          font-weight: 500;
          color: #222222;
          text-align: center;
          margin-bottom: 32px;
          line-height: 1.25;
      }

      .dropdown-content .projects-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 12px;
          margin-bottom: 16px;
      }

      .dropdown-content .project-card {
          background: white;
          border: 1px solid #dddddd;
          border-radius: 12px;
          padding: 24px 34px;
          cursor: pointer;
          transition: all 0.2s ease;
      }

      .dropdown-content .project-card:hover {
          border-color: #222222;
      }

      .dropdown-content .project-card.active {
          background: #f7f7f7;
          border: 2px solid #222222;
      }

      .dropdown-content .project-name {
          font-weight: 500;
          color: #222222;
          margin-bottom: 8px;
          font-size: 16px;
          line-height: 1.25;
      }

      .dropdown-content .project-date {
          color: #717171;
          font-size: 14px;
          line-height: 1.43;
      }

      .dropdown-content .timeline-view h3 {
          font-size: 18px;
          font-weight: 500;
          color: #222222;
          text-align: center;
          margin-bottom: 14px;
          line-height: 1.25;
      }

      .dropdown-content .time-options {
          display: flex;
          justify-content: center;
          gap: 8px;
          margin-bottom: 48px;
      }

      .dropdown-content .time-option {
          padding: 9px 16px;
          border-radius: 32px;
          background: white;
          border: 1px solid #dddddd;
          cursor: pointer;
          transition: all 0.25s ease;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          font-family: inherit;
      }

      .dropdown-content .time-option:hover {
          border-color: #717171;
      }

      .dropdown-content .time-option.active {
          background: #f7f7f7;
          border: 2px solid #222222;
          color: #222222;
      }

      .dropdown-content .timeline-question {
          font-size: 18px;
          font-weight: 500;
          color: #222222;
          text-align: center;
          margin-bottom: 18px;
          line-height: 1.25;
      }



      .dropdown-content .hidden {
          display: none;
      }

      /* 导航按钮的额外样式（不与现有样式冲突） */
      .dropdown-content .nav-button:hover {
          border-color: #cccccc;
          box-shadow: 0 0 0 1px transparent, 0 0 0 4px transparent, 0 6px 16px rgba(0,0,0,0.12);
      }

      .dropdown-content #prev-months {
          margin-right: -22px;
      }

      .dropdown-content #next-months {
          margin-left: -22px;
      }

      /* 月份网格的额外样式 */
      .dropdown-content .months-grid {
          display: flex;
          gap: 8px;
      }

      /* 月份卡片的额外样式（与现有样式融合） */
      .dropdown-content .month-card:hover {
          border-color: #222222;
      }

      .dropdown-content .month-card.active {
          background: #f7f7f7;
          border: 2px solid #222222;
      }

      .dropdown-content .month-card.active:hover {
          background: #f7f7f7;
          border: 2px solid #222222;
      }

      .dropdown-content .month-icon {
          opacity: 0.7;
      }

      .dropdown-content .month-card.active .month-icon {
          opacity: 1;
      }


  </style>
  <script>
  const ANIMATION_CONFIG = {
      durationTransform: 0.3, // transform 动画时长（秒）
      durationOpacity: 0.5,   // opacity 动画时长（秒）
      easing: ' cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      startDelay: 0           // 延迟补偿（秒）
  };
  function getTransitionString() {
      const t = ANIMATION_CONFIG;
      return `transform ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
              opacity ${t.durationOpacity}s ${t.easing} ${t.startDelay}s, 
              left ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
              top ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
              width ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
              height ${t.durationTransform}s ${t.easing} ${t.startDelay}s`;
  }
  </script>

</head>
<body>
  <div class="search-container">
      <div class="search-bar" id="searchBar">
          <div class="active-indicator" id="activeIndicator"></div>
          <div class="search-item" data-type="location">
              <input type="text" class="search-input" id="locationInput" placeholder="选择机构或关键字搜索" />
              <button class="clear-button" id="locationClear">
                  <svg viewBox="0 0 24 24" fill="none">
                      <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
              </button>
          </div>
          <div class="search-separator"></div>
          <div class="search-item" data-type="guests">
              <input type="text" class="search-input" id="guestsInput" placeholder="沟通记录" readonly />
              <button class="clear-button" id="guestsClear">
                  <svg viewBox="0 0 24 24" fill="none">
                      <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
              </button>
          </div>
          <button class="search-button" id="searchButton">
              <svg class="search-icon" viewBox="0 0 32 32">
                  <path d="M13 24c6.075 0 11-4.925 11-11S19.075 2 13 2 2 6.925 2 13s4.925 11 11 11zm8-3l9 9-3 3-9-9v-2h2z"/>
              </svg>
              <span class="search-button-text">搜索</span>
          </button>
      </div>

      <div class="dropdown-container" id="dropdownContainer">
          <div class="dropdown-content" id="dropdownContent">
              <!-- 内容将通过JavaScript动态填充 -->
          </div>
      </div>
  </div>

  <script>
      class AirbnbSearchBox {
          constructor() {
              this.currentActiveType = null;
              this.guestCounts = { adults: 0, children: 0, infants: 0 };
              this.selectedLocation = '';
              this.initElements();
              this.bindEvents();
              this.setupGuestCounters();
          }

          initElements() {
              this.searchBar = document.getElementById('searchBar');
              this.activeIndicator = document.getElementById('activeIndicator');
              this.dropdownContainer = document.getElementById('dropdownContainer');
              this.dropdownContent = document.getElementById('dropdownContent');
              this.locationInput = document.getElementById('locationInput');
              this.locationClear = document.getElementById('locationClear');
              this.guestsInput = document.getElementById('guestsInput');
              this.guestsClear = document.getElementById('guestsClear');
              this.searchItems = document.querySelectorAll('.search-item');
              this.searchSeparator = document.querySelector('.search-separator');
              this.searchButton = document.getElementById('searchButton');

              // 选择状态
              this.selectedProjects = [];
              this.selectedTimeOptions = [];
              this.selectedMonths = [];
              this.currentTab = 'projects'; // 'projects' 或 'timeline'
          }

          bindEvents() {
              this.searchItems.forEach(item => {
                  item.addEventListener('click', (e) => {
                      e.stopPropagation();
                      const type = item.dataset.type;
                      this.showDropdown(type, item);
                  });
                  item.addEventListener('mouseover', (e) => this.handleSearchItemHover(e.currentTarget, true));
                  item.addEventListener('mouseout', (e) => this.handleSearchItemHover(e.currentTarget, false));
              });

              document.addEventListener('click', (e) => {
                  if (!e.target.closest('.search-container')) {
                      this.hideDropdown();
                  }
              });

              document.getElementById('searchButton').addEventListener('click', () => {
                  this.performSearch();
              });

              // 输入框事件处理
              this.locationInput.addEventListener('input', (e) => {
                  this.handleInputChange(e.target.value);
              });

              this.locationInput.addEventListener('focus', () => {
                  const locationItem = document.querySelector('[data-type="location"]');
                  this.showDropdown('location', locationItem);
              });

              // 清除按钮事件处理
              this.locationClear.addEventListener('click', (e) => {
                  e.stopPropagation();
                  this.clearLocationInput();
              });

              // 清除按钮悬停效果
              this.locationClear.addEventListener('mouseenter', () => {
                  if (this.locationInput.value.trim() && this.activeIndicator.classList.contains('show')) {
                      this.locationClear.style.transform = 'translateY(-50%) scale(1.1)';
                  }
              });

              this.locationClear.addEventListener('mouseleave', () => {
                  if (this.locationInput.value.trim() && this.activeIndicator.classList.contains('show')) {
                      this.locationClear.style.transform = 'translateY(-50%) scale(1)';
                  }
              });

              // 沟通记录输入框事件处理
              this.guestsInput.addEventListener('focus', () => {
                  const guestsItem = document.querySelector('[data-type="guests"]');
                  this.showDropdown('guests', guestsItem);
              });

              // 沟通记录清除按钮事件处理
              this.guestsClear.addEventListener('click', (e) => {
                  e.stopPropagation();
                  this.clearGuestsInput();
              });

              // 沟通记录清除按钮悬停效果
              this.guestsClear.addEventListener('mouseenter', () => {
                  if (this.guestsInput.value.trim() && this.activeIndicator.classList.contains('show')) {
                      this.guestsClear.style.transform = 'translateY(-50%) scale(1.1)';
                  }
              });

              this.guestsClear.addEventListener('mouseleave', () => {
                  if (this.guestsInput.value.trim() && this.activeIndicator.classList.contains('show')) {
                      this.guestsClear.style.transform = 'translateY(-50%) scale(1)';
                  }
              });

              this.activeIndicator.addEventListener('click', (e) => {
                  e.stopPropagation();
              });
          }

          showDropdown(type, activeItem) {
              if (this.currentActiveType === type) {
                  return;
              }

              const isFirstDisplay = !this.dropdownContainer.classList.contains('show');
              const isMenuSizeChange = this.currentActiveType && this.currentActiveType !== type;
              const isPositionChange = this.currentActiveType && this.currentActiveType !== type;

              // FLIP 技法：First - 记录当前状态
              let flipData = null;
              if (isMenuSizeChange) {
                  const oldRect = this.dropdownContainer.getBoundingClientRect();
                  const currentStyles = getComputedStyle(this.dropdownContainer);
                  flipData = {
                      oldWidth: oldRect.width,
                      oldHeight: oldRect.height,
                      oldLeft: oldRect.left - this.searchBar.getBoundingClientRect().left,
                      oldTop: oldRect.top
                  };
              }

              this.currentActiveType = type;
              this.searchItems.forEach(item => item.classList.remove('active'));
              activeItem.classList.add('active');

              // 根据显示类型设置active-indicator的过渡效果
              if (isFirstDisplay) {
                  // 首次显示时禁用active-indicator的过渡，确保直接出现在正确位置
                  this.activeIndicator.style.setProperty('transition', 'none', 'important');
                  this.dropdownContainer.style.transition = 'none';

                  // 强制重绘确保过渡禁用生效
                  this.activeIndicator.offsetHeight;
                  this.dropdownContainer.offsetHeight;
              } else if (isPositionChange) {
                  // 位置切换时，启用active-indicator的滑动过渡
                  this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                  this.dropdownContainer.style.transition = 'none';
              }

              // FLIP 技法：Last - 更新内容但不改变位置
              if (isMenuSizeChange && flipData) {
                  // 先临时隐藏，更新内容，然后恢复位置
                  this.dropdownContainer.style.visibility = 'hidden';
                  this.updateDropdownContent(type);

                  // 获取新内容的自然高度
                  this.dropdownContainer.style.width = (type === 'guests' ? 850 : 407) + 'px';
                  this.dropdownContainer.style.height = 'auto';
                  const newHeight = this.dropdownContainer.offsetHeight;

                  // Invert: 恢复到起始状态
                  this.dropdownContainer.style.left = flipData.oldLeft + 'px';
                  this.dropdownContainer.style.width = flipData.oldWidth + 'px';
                  this.dropdownContainer.style.height = flipData.oldHeight + 'px';
                  this.dropdownContainer.style.transformOrigin = 'top left';
                  this.dropdownContainer.style.visibility = 'visible';

                  // Invert: 恢复到起始状态
                  this.updateActiveIndicator(activeItem);

                  // 强制重绘
                  this.dropdownContainer.offsetHeight;

                  // Play: 启用过渡并动画到最终状态 - 使用统一动画配置
                  this.dropdownContainer.style.transition = getTransitionString();

                  requestAnimationFrame(() => {
                      this.dropdownContainer.style.width = (type === 'guests' ? 850 : 407) + 'px';
                      this.dropdownContainer.style.height = newHeight + 'px';
                  });
              } else {
                  // 首次显示或位置变化
                  this.updateDropdownContent(type);
                  this.positionDropdown(activeItem);

                  if (isFirstDisplay) {
                      // 首次显示：设置初始动画状态，从小到大出现
                      this.dropdownContainer.style.opacity = '0.2';
                      this.dropdownContainer.style.transform = 'scale(0.5)';
                      this.dropdownContainer.style.transformOrigin = 'top center';
                      this.dropdownContainer.style.visibility = 'visible';

                      // 强制重绘
                      this.dropdownContainer.offsetHeight;
                      this.activeIndicator.offsetHeight;

                      // 启用过渡并动画到最终状态 - 使用统一动画配置
                      this.dropdownContainer.style.transition = getTransitionString();
                      // 首次显示时，active-indicator不需要过渡动画
                      this.activeIndicator.style.removeProperty('transition');

                      // 使用 requestAnimationFrame 确保动画生效
                      requestAnimationFrame(() => {
                          this.dropdownContainer.style.opacity = '1';
                          this.dropdownContainer.style.transform = 'scale(1)';
                      });
                  } else if (isPositionChange) {
                      // 位置变化时：使用滑动动画，菜单从旧位置滑动到新位置
                      this.dropdownContainer.style.opacity = '1';
                      this.dropdownContainer.style.transform = 'scale(1)';
                      this.dropdownContainer.style.visibility = 'visible';

                      // 强制重绘，确保过渡设置生效
                      this.dropdownContainer.offsetHeight;

                      // 在下一帧恢复完整的过渡设置
                      requestAnimationFrame(() => {
                          this.dropdownContainer.style.setProperty('transition', getTransitionString(), 'important');
                          this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                      });
                  } else {
                      // 其他情况
                      this.dropdownContainer.offsetHeight;
                      this.activeIndicator.offsetHeight;
                  }
              }

              // 根据显示类型更新active-indicator
              if (isFirstDisplay) {
                  // 首次显示：直接更新位置，无动画
                  this.updateActiveIndicator(activeItem);

                  // 在下一帧恢复active-indicator的过渡设置
                  requestAnimationFrame(() => {
                      this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                  });
              } else if (isPositionChange) {
                  // 位置切换：先更新位置，让滑动动画生效
                  this.updateActiveIndicator(activeItem);

                  // 在动画完成后恢复默认过渡设置
                  setTimeout(() => {
                      this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                  }, 300);
              } else {
                  // 其他情况：正常更新
                  this.updateActiveIndicator(activeItem);
              }

              this.searchBar.classList.add('expanded');
              this.dropdownContainer.classList.add('show');
              this.activeIndicator.classList.add('show');
              this.searchButton.classList.add('expanded');

              // 更新清除按钮的显示状态
              this.updateClearButtonVisibility();
          }

          hideDropdown() {
              this.currentActiveType = null;
              this.searchBar.classList.remove('expanded');
              this.searchBar.classList.remove('hovering-item');

              // 强制禁用所有过渡效果，使用!important级别的覆盖
              this.dropdownContainer.style.setProperty('transition', 'none', 'important');
              this.activeIndicator.style.setProperty('transition', 'none', 'important');

              // 强制重绘以确保transition设置生效
              this.dropdownContainer.offsetHeight;

              // 立即设置隐藏状态并清理所有临时样式
              this.dropdownContainer.style.visibility = 'hidden';
              this.dropdownContainer.style.opacity = '0';
              this.dropdownContainer.style.transform = 'scale(0.5)';

              // 清理所有可能影响高度和尺寸的临时样式
              this.dropdownContainer.style.width = '';
              this.dropdownContainer.style.height = '';
              this.dropdownContainer.style.left = '';
              this.dropdownContainer.style.transformOrigin = '';

              // 重置active-indicator的位置样式，确保下次显示时不会从旧位置滑动
              this.activeIndicator.style.left = '';
              this.activeIndicator.style.top = '';
              this.activeIndicator.style.width = '';
              this.activeIndicator.style.height = '';
              this.activeIndicator.style.transform = '';
              this.activeIndicator.style.transformOrigin = '';

              this.dropdownContainer.classList.remove('show');
              this.activeIndicator.classList.remove('show');
              this.searchItems.forEach(item => item.classList.remove('active'));
              this.searchButton.classList.remove('expanded');

              // 隐藏清除按钮
              this.updateClearButtonVisibility();

              // 在下一帧清理transition设置，为下次显示做准备
              requestAnimationFrame(() => {
                  this.dropdownContainer.style.removeProperty('transition');
                  this.activeIndicator.style.removeProperty('transition');
              });
          }

          updateActiveIndicator(activeItem) {
              const itemRect = activeItem.getBoundingClientRect();
              const searchBarRect = this.searchBar.getBoundingClientRect();

              const computedStyle = getComputedStyle(this.searchBar);
              const borderLeftWidth = parseFloat(computedStyle.borderLeftWidth);
              const borderTopWidth = parseFloat(computedStyle.borderTopWidth);
              const borderRightWidth = parseFloat(computedStyle.borderRightWidth);

              const left = (itemRect.left - searchBarRect.left) - borderLeftWidth;
              const top = (itemRect.top - searchBarRect.top) - borderTopWidth;
              
              let width = itemRect.width;

              if (activeItem.dataset.type === 'guests') {
                  width = (searchBarRect.width - borderLeftWidth - borderRightWidth) - left;
              }
              
              this.activeIndicator.style.left = left + 'px';
              this.activeIndicator.style.width = width + 'px';
              this.activeIndicator.style.height = '100%';
              this.activeIndicator.style.top = top + 'px';
          }

          positionDropdown(activeItem) {
              const searchBarRect = this.searchBar.getBoundingClientRect();
              const itemRect = activeItem.getBoundingClientRect();
              const type = activeItem.dataset.type;
              const containerWidth = type === 'guests' ? 850 : 407;

              // 首次显示时的定位逻辑
              if (type === 'location') {
                  // 小菜单居中定位
                  const activeItemCenter = (itemRect.left - searchBarRect.left) + (itemRect.width / 2);
                  let left = activeItemCenter - (containerWidth / 2);
                  const maxLeft = searchBarRect.width - containerWidth;
                  left = Math.max(0, Math.min(left, maxLeft));

                  this.dropdownContainer.style.left = left + 'px';
                  this.dropdownContainer.style.transformOrigin = '50% top';
              } else {
                  // 大菜单（沟通记录）左对齐，为扩展动画做准备
                  this.dropdownContainer.style.left = '0px';
                  this.dropdownContainer.style.transformOrigin = 'top left';
              }

              this.dropdownContainer.style.width = containerWidth + 'px';
          }

          updateDropdownContent(type) {
              if (type === 'location') {
                  this.dropdownContent.innerHTML = this.getLocationContent();
                  this.bindLocationEvents();
              } else if (type === 'guests') {
                  // 融入 a.html 的全部功能
                  this.dropdownContent.innerHTML = this.getTimePickerContent();
                  this.bindGuestEvents();
              }
          }

          getTimePickerContent() {
            // 动态生成今年8月（2025年8月）往前12个月的月份卡片
            const now = new Date();
            // 固定到2025年8月
            let year = 2025, month = 8;
            // 构造12个月份
            let months = [];
            for (let i = 0; i < 12; i++) {
              let m = month - i;
              let y = year;
              if (m < 1) {
                y -= Math.ceil((1 - m) / 12);
                m = ((m - 1 + 12 * 2) % 12) + 1;
              }
              months.unshift({ y, m });
            }
            const monthIconSVG = `<svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: #6a6a6a;"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>`;
            const monthsCardsHTML = months.map(({ y, m }) => {
              return `<div class="month-card">${monthIconSVG}<div class="month-name">${m} 月</div><div class="month-year">${y}</div></div>`;
            }).join('');
            return `
                <div class="container" style="max-width: none; padding: 0; margin: 0;">
                    <div class="dropdown" style="border: none; box-shadow: none; border-radius: 0;">
                        <div class="dropdown-header">
                            <div class="tabs-container">
                                <button class="tab active" id="tab-projects">WorkFeed</button>
                                <button class="tab" id="tab-timeline">最近沟通</button>
                            </div>
                        </div>
                        <div class="dropdown-content">
                            <div class="projects-view" id="projects-view">
                                <h3>选择相关WorkFeed</h3>
                                <div class="projects-grid">
                                    <div class="project-card"><div class="project-name">这是一个非常长的项目名称用来测试截断功能</div><div class="project-date">8月7日</div></div>
                                    <div class="project-card"><div class="project-name">短项目名</div><div class="project-date">8月7日</div></div>
                                    <div class="project-card"><div class="project-name">中等长度的项目名称</div><div class="project-date">8月7日</div></div>
                                    <div class="project-card"><div class="project-name">另一个超级长的项目名称用于测试多选时的显示效果</div><div class="project-date">8月8日</div></div>
                                    <div class="project-card"><div class="project-name">普通项目</div><div class="project-date">8月8日</div></div>
                                    <div class="project-card"><div class="project-name">最后一个项目</div><div class="project-date">8月8日</div></div>
                                </div>
                            </div>
                            <div class="timeline-view hidden" id="timeline-view">
                                <h3>您多久前沟通的？</h3>
                                <div class="time-options">
                                    <button class="time-option">本周</button>
                                    <button class="time-option active">上周</button>
                                    <button class="time-option">近两周</button>
                                </div>
                                <div class="timeline-question">您上次是几月沟通的？</div>
                                <div class="months-container" id="months-container">
                                    <button class="nav-button prev" id="prev-months">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" style="width: 16px; height: 16px; fill: none; stroke: currentcolor; stroke-width: 2;">
                                            <path d="M20 28 8.7 16.7a1 1 0 0 1 0-1.4L20 4"></path>
                                        </svg>
                                    </button>
                                    <div class="months-viewport">
                                        <div class="months-track" id="months-track">
                                            <!-- 月份卡片将通过JavaScript动态生成 -->
                                        </div>
                                    </div>
                                    <button class="nav-button next" id="next-months">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" style="width: 16px; height: 16px; fill: none; stroke: currentcolor; stroke-width: 2;">
                                            <path d="m12 4 11.3 11.3a1 1 0 0 1 0 1.4L12 28"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
          }

          getLocationContent() {
              const locations = [
                  { name: '乌鲁木齐分行', desc: '机构，27个部门' },
                  { name: '克拉玛依分行', desc: '机构，25个部门' },
                  { name: '库尔勒分行', desc: '机构，16个部门' },
                  { name: '喀什分行', desc: '机构，18个部门' },
                  { name: '伊犁分行', desc: '机构，22个部门' },
                  { name: '阿克苏分行', desc: '机构，15个部门' },
                  { name: '哈密分行', desc: '机构，12个部门' },
                  { name: '昌吉分行', desc: '机构，14个部门' },
                  { name: '博州分行', desc: '机构，11个部门' },
                  { name: '吐鲁番分行', desc: '机构，9个部门' },
                  { name: '塔城分行', desc: '机构，13个部门' },
                  { name: '阿勒泰分行', desc: '机构，10个部门' }
              ];

              return `
                  <div class="dropdown-section">
                      <div class="dropdown-title">全部 · 选择机构 · ${locations.length}项</div>
                      <div class="location-list">
                          ${locations.map(location => `
                          <div class="location-item" data-location="${location.name}">
                              <div class="location-icon">
                                  <svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="presentation" focusable="false" style="display: block; fill: none; height: 32px; width: 32px; stroke: currentcolor; stroke-width: 1.5; overflow: visible;">
                                      <g stroke-width="1.5">
                                          <path d="m16 7.75c1.1735972 0 2.2360984.47570107 3.0051998 1.2448025.7690986.76909855 1.2448002 1.8315962 1.2448002 3.0051975 0 1.173595-.4757042 2.236094-1.2448051 3.0051949s-1.8315999 1.2448051-3.0051949 1.2448051-2.236094-.4757042-3.0051949-1.2448051-1.2448051-1.8315999-1.2448051-3.0051949c0-1.1736013.4757016-2.23609895 1.2448002-3.0051975.7691014-.76910143 1.8316026-1.2448025 3.0051998-1.2448025z"></path>
                                          <path d="m16 .75c3.1065961 0 5.9190979 1.2592006 7.9549498 3.2950525 2.0358488 2.03584879 3.2950502 4.84834676 3.2950502 7.9549475 0 6.0484567-3.6326743 12.1363419-10.665218 18.3219506l-.584789.5068533-.5806596-.5032601c-7.03664913-6.1892017-10.6693334-12.277087-10.6693334-18.3255438 0-3.08608466 1.26862562-5.90040631 3.31064245-7.94242314 2.03802515-2.03802519 4.84740855-3.30757686 7.93935755-3.30757686z"></path>
                                      </g>
                                  </svg>
                              </div>
                              <div class="location-content">
                                  <div class="location-name">${location.name}</div>
                                  <div class="location-desc">${location.desc}</div>
                              </div>
                          </div>
                      `).join('')}
                      </div>
                  </div>
              `;
          }

          bindLocationEvents() {
              const locationItems = this.dropdownContent.querySelectorAll('.location-item');
              locationItems.forEach(item => {
                  item.addEventListener('click', () => {
                      const location = item.dataset.location;
                      this.selectedLocation = location;
                      this.locationInput.value = location;
                      this.handleInputChange(location);
                      this.hideDropdown();
                  });
              });
          }

          // 重构的月份容器初始化方法
          initMonthsContainer() {
              // 生成月份数据（从2024年9月到2025年8月，共12个月）
              const months = [];
              const currentYear = 2025;
              const currentMonth = 8; // 8月

              for (let i = 11; i >= 0; i--) {
                  let month = currentMonth - i;
                  let year = currentYear;

                  if (month <= 0) {
                      month += 12;
                      year -= 1;
                  }

                  months.push({ year, month });
              }

              // 生成月份卡片HTML
              const monthsTrack = document.getElementById('months-track');
              if (!monthsTrack) return;

              const monthIconSVG = `<svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
                  <path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>
              </svg>`;

              monthsTrack.innerHTML = months.map(({ year, month }) => `
                  <div class="month-card" data-year="${year}" data-month="${month}">
                      ${monthIconSVG}
                      <div class="month-name">${month}月</div>
                      <div class="month-year">${year}</div>
                  </div>
              `).join('');

              // 初始化滑动控制
              this.currentMonthIndex = 0; // 从最早的月份开始
              this.monthsData = months;
              this.monthsTrack = monthsTrack;
              this.monthsViewport = document.querySelector('.months-viewport');

              // 计算每次显示的月份数量（基于容器宽度）
              this.updateVisibleMonths();

              // 默认显示最近的月份（右对齐显示最后几个月）
              this.showRecentMonths();

              // 绑定导航按钮事件
              this.bindMonthsNavigation();

              // 绑定月份卡片点击事件
              this.bindMonthCardEvents();

              // 监听窗口大小变化
              window.addEventListener('resize', () => this.updateVisibleMonths());
          }

          updateVisibleMonths() {
              if (!this.monthsViewport) return;

              // 固定显示6个月份卡片
              this.visibleCount = 6;
              this.maxIndex = Math.max(0, this.monthsData.length - this.visibleCount);

              // 调整容器宽度以正好容纳6个卡片，确保完整显示
              const cardWidth = 80; // 卡片宽度
              const gap = 16; // 卡片间距
              const padding = 20; // 左右内边距，确保卡片不被遮挡
              const totalWidth = (cardWidth * this.visibleCount) + (gap * (this.visibleCount - 1)) + padding;
              this.monthsViewport.style.width = totalWidth + 'px';
          }

          showRecentMonths() {
              // 显示最近的月份，让当前月份（最后一个）显示在右侧
              this.currentMonthIndex = this.maxIndex;
              this.updateMonthsPosition();
              this.updateNavigationButtons();
          }

          updateMonthsPosition() {
              if (!this.monthsTrack) return;

              const cardWidth = 122; // 卡片宽度
              const gap = 12; // 卡片间距
              const translateX = -this.currentMonthIndex * (cardWidth + gap);
              this.monthsTrack.style.transform = `translateX(${translateX}px)`;
          }

          updateNavigationButtons() {
              const prevButton = document.getElementById('prev-months');
              const nextButton = document.getElementById('next-months');

              if (prevButton) {
                  prevButton.disabled = this.currentMonthIndex <= 0;
              }

              if (nextButton) {
                  nextButton.disabled = this.currentMonthIndex >= this.maxIndex;
              }
          }

          bindMonthsNavigation() {
              const prevButton = document.getElementById('prev-months');
              const nextButton = document.getElementById('next-months');

              if (prevButton) {
                  prevButton.addEventListener('click', () => {
                      // 点击一次显示两个月份（向前滑动2个位置）
                      const newIndex = Math.max(0, this.currentMonthIndex - 2);
                      if (newIndex !== this.currentMonthIndex) {
                          this.currentMonthIndex = newIndex;
                          this.updateMonthsPosition();
                          this.updateNavigationButtons();
                      }
                  });
              }

              if (nextButton) {
                  nextButton.addEventListener('click', () => {
                      // 点击一次显示两个月份（向后滑动2个位置）
                      const newIndex = Math.min(this.maxIndex, this.currentMonthIndex + 2);
                      if (newIndex !== this.currentMonthIndex) {
                          this.currentMonthIndex = newIndex;
                          this.updateMonthsPosition();
                          this.updateNavigationButtons();
                      }
                  });
              }
          }

          bindMonthCardEvents() {
              const monthCards = this.monthsTrack.querySelectorAll('.month-card');
              const defaultIconPath = 'M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z';
              const selectedIconPath = 'M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v16.07a2 2 0 0 1-.46 1.28l-.12.13L21 29.75a2 2 0 0 1-1.24.58H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h11.18v-5.66a5 5 0 0 1 4.78-5h5.88zm-.08 8h-5.58a3 3 0 0 0-3 2.82v5.76zm-18.58-16h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z';

              monthCards.forEach(card => {
                  card.addEventListener('click', () => {
                      // 获取月份信息
                      const monthName = card.querySelector('.month-name').textContent;
                      const monthYear = card.querySelector('.month-year').textContent;
                      const monthKey = `${monthYear}-${monthName}`;

                      if (card.classList.contains('active')) {
                          // 取消选择
                          card.classList.remove('active');
                          const icon = card.querySelector('.month-icon path');
                          if (icon) {
                              icon.setAttribute('d', defaultIconPath);
                              card.querySelector('.month-icon').style.fill = '#6a6a6a';
                          }
                          this.selectedMonths = this.selectedMonths.filter(m => m !== monthKey);
                      } else {
                          // 选择月份（可以多选）
                          card.classList.add('active');
                          const selectedIcon = card.querySelector('.month-icon path');
                          if (selectedIcon) {
                              selectedIcon.setAttribute('d', selectedIconPath);
                              card.querySelector('.month-icon').style.fill = '#ff385c';
                          }
                          this.selectedMonths.push(monthKey);
                      }

                      // 清除时间选项选择（互斥）
                      const timeOptions = document.querySelectorAll('.time-option');
                      timeOptions.forEach(opt => opt.classList.remove('active'));
                      this.selectedTimeOptions = [];

                      // 清除其他选择状态
                      this.clearOtherSelections();
                      // 更新输入框内容
                      this.updateGuestsInputValue();
                  });
              });
          }

          clearOtherSelections() {
              // 清除时间选项和项目卡片的选择状态
              const timeOptions = document.querySelectorAll('.time-option');
              const projectCards = document.querySelectorAll('.project-card');

              timeOptions.forEach(opt => opt.classList.remove('active'));
              projectCards.forEach(card => card.classList.remove('active'));

              // 同时清除对应的数据状态
              this.selectedTimeOptions = [];
              this.selectedProjects = [];
          }

          clearGuestsSelections() {
              // 清除沟通记录的所有选择状态
              this.selectedProjects = [];
              this.selectedTimeOptions = [];
              this.selectedMonths = [];
              this.guestsInput.value = '';
              this.updateClearButtonVisibility();

              // 清除UI选中状态
              this.clearAllSelections();
          }

          clearAllSelections() {
              // 清除所有UI选中状态
              const timeOptions = document.querySelectorAll('.time-option');
              const projectCards = document.querySelectorAll('.project-card');
              const monthCards = document.querySelectorAll('.month-card');

              timeOptions.forEach(opt => opt.classList.remove('active'));
              projectCards.forEach(card => card.classList.remove('active'));
              monthCards.forEach(card => {
                  card.classList.remove('active');
                  const icon = card.querySelector('.month-icon path');
                  if (icon) {
                      icon.setAttribute('d', 'M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z');
                      card.querySelector('.month-icon').style.fill = '#6a6a6a';
                  }
              });
          }

          updateGuestsInputValue() {
              let displayText = '';

              if (this.currentTab === 'projects') {
                  // 项目标签：显示项目名称 + 沟通记录
                  if (this.selectedProjects.length > 0) {
                      if (this.selectedProjects.length === 1) {
                          // 只选择一个项目：显示项目名称+沟通记录，如果名称太长则显示省略号
                          const projectName = this.selectedProjects[0];
                          displayText = this.truncateProjectName(projectName) + '沟通记录';
                      } else {
                          // 选择多个项目：显示第一个项目名称+等X个项目沟通记录
                          const firstProjectName = this.selectedProjects[0];
                          const truncatedName = this.truncateProjectName(firstProjectName);
                          displayText = truncatedName + '等' + this.selectedProjects.length + '个项目沟通记录';
                      }
                  }
              } else if (this.currentTab === 'timeline') {
                  // 时间线标签：time-option和months互斥显示
                  if (this.selectedTimeOptions.length > 0) {
                      // time-option单选显示
                      displayText = this.selectedTimeOptions[0] + '沟通记录';
                  } else if (this.selectedMonths.length > 0) {
                      // months多选，按年-月格式显示，用顿号分开
                      const sortedMonths = this.selectedMonths.sort();
                      displayText = sortedMonths.join('、') + '沟通记录';
                  }
              }

              this.guestsInput.value = displayText;
              this.updateClearButtonVisibility();
          }

          // 新增方法：截断项目名称，如果超过100px则显示省略号
          truncateProjectName(projectName) {
              // 创建一个临时元素来测量文本宽度
              const tempElement = document.createElement('span');
              tempElement.style.visibility = 'hidden';
              tempElement.style.position = 'absolute';
              tempElement.style.fontSize = '14px'; // 与输入框字体大小一致
              tempElement.style.fontFamily = getComputedStyle(this.guestsInput).fontFamily;
              tempElement.textContent = projectName;
              document.body.appendChild(tempElement);

              const textWidth = tempElement.offsetWidth;
              document.body.removeChild(tempElement);

              // 如果文本宽度超过100px，则截断并添加省略号
              if (textWidth > 100) {
                  // 二分法找到合适的截断长度
                  let left = 0;
                  let right = projectName.length;
                  let result = projectName;

                  while (left < right) {
                      const mid = Math.floor((left + right + 1) / 2);
                      const testText = projectName.substring(0, mid) + '...';

                      tempElement.textContent = testText;
                      document.body.appendChild(tempElement);
                      const testWidth = tempElement.offsetWidth;
                      document.body.removeChild(tempElement);

                      if (testWidth <= 100) {
                          left = mid;
                          result = testText;
                      } else {
                          right = mid - 1;
                      }
                  }

                  return result;
              }

              return projectName;
          }



          bindGuestEvents() {
            // 初始化月份容器
            this.initMonthsContainer();

            const tabProjects = document.getElementById('tab-projects');
            const tabTimeline = document.getElementById('tab-timeline');
            const projectsView = document.getElementById('projects-view');
            const timelineView = document.getElementById('timeline-view');
            const projectCards = document.querySelectorAll('.project-card');
            const timeOptions = document.querySelectorAll('.time-option');

            const defaultIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';
            const selectedIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v16.07a2 2 0 0 1-.46 1.28l-.12.13L21 29.75a2 2 0 0 1-1.24.58H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h11.18v-5.66a5 5 0 0 1 4.78-5h5.88zm-.08 8h-5.58a3 3 0 0 0-3 2.82v5.76zm-18.58-16h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';

            // 添加高度动画的标签页切换函数
            const switchTab = (targetTab, targetView, otherView) => {
                if (targetTab.classList.contains('active')) return;
                const dropdownContent = document.querySelector('.dropdown-content');
                const currentHeight = dropdownContent.offsetHeight;
                document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
                targetTab.classList.add('active');
                otherView.classList.add('hidden');
                targetView.classList.remove('hidden');
                const newHeight = dropdownContent.offsetHeight;
                if (currentHeight === newHeight) return;
                dropdownContent.style.height = currentHeight + 'px';
                dropdownContent.style.transition = 'height 0.15s cubic-bezier(0.15, 0, 0.1, 1)';
                dropdownContent.offsetHeight;
                requestAnimationFrame(() => {
                    dropdownContent.style.height = newHeight + 'px';
                    setTimeout(() => {
                        dropdownContent.style.height = '';
                        dropdownContent.style.transition = '';
                    }, 300);
                });
            };

            tabProjects.addEventListener('click', () => {
                switchTab(tabProjects, projectsView, timelineView);
                // 切换到项目标签时，如果有内容先清除，然后设置当前标签
                if (this.guestsInput.value.trim()) {
                    this.clearGuestsSelections();
                }
                this.currentTab = 'projects';
            });
            tabTimeline.addEventListener('click', () => {
                switchTab(tabTimeline, timelineView, projectsView);
                // 切换到时间线标签时，如果有内容先清除，然后设置当前标签
                if (this.guestsInput.value.trim()) {
                    this.clearGuestsSelections();
                }
                this.currentTab = 'timeline';
            });

            function clearTimeOptions() { timeOptions.forEach(opt => opt.classList.remove('active')); }
            function clearMonthCards() {
                const monthCards = document.querySelectorAll('.month-card');
                monthCards.forEach(c => {
                    c.classList.remove('active');
                    const icon = c.querySelector('.month-icon');
                    if (icon) { icon.style.fill = '#6a6a6a'; }
                });
            }
            function clearProjectCards() { projectCards.forEach(p => p.classList.remove('active')); }

            timeOptions.forEach(option => {
                option.addEventListener('click', () => {
                    clearMonthCards();
                    clearProjectCards();

                    const timeText = option.textContent;

                    if (option.classList.contains('active')) {
                        // 取消选择
                        option.classList.remove('active');
                        this.selectedTimeOptions = [];
                    } else {
                        // 单选：先清除其他选项，再选择当前选项
                        timeOptions.forEach(opt => opt.classList.remove('active'));
                        option.classList.add('active');
                        this.selectedTimeOptions = [timeText];
                    }

                    // 清除月份选择和项目选择（互斥）
                    this.selectedMonths = [];
                    this.selectedProjects = [];

                    this.updateGuestsInputValue();
                });
            });

            // 月份卡片事件已在 initMonthsContainer 中绑定
            projectCards.forEach(card => {
                card.addEventListener('click', () => {
                    clearTimeOptions();
                    clearMonthCards();

                    // 清除其他选项的数据状态（互斥）
                    this.selectedTimeOptions = [];
                    this.selectedMonths = [];

                    const projectName = card.querySelector('.project-name').textContent;

                    if (card.classList.contains('active')) {
                        // 取消选择
                        card.classList.remove('active');
                        this.selectedProjects = this.selectedProjects.filter(p => p !== projectName);
                    } else {
                        // 选择项目（无限制）
                        card.classList.add('active');
                        this.selectedProjects.push(projectName);
                    }

                    this.updateGuestsInputValue();
                });
            });


            // 导航按钮事件已在 initMonthsContainer 中绑定
          }

          changeGuestCount(type, change) {
              const newCount = Math.max(0, this.guestCounts[type] + change);
              this.guestCounts[type] = newCount;
              const el = document.getElementById(type + 'Count');
              if (el) el.textContent = newCount;
              this.updateGuestsText();
              this.updateCounterStates();
          }

          updateCounterStates() {
              Object.keys(this.guestCounts).forEach(type => {
                  const minusBtn = this.dropdownContent.querySelector(`[data-type="${type}"][data-action="minus"]`);
                  if (minusBtn) { minusBtn.disabled = this.guestCounts[type] === 0; }
              });
          }

          updateGuestsText() {
              const total = Object.values(this.guestCounts).reduce((sum, count) => sum + count, 0);
              if (total === 0) { this.guestsText.textContent = '沟通记录'; }
              else { this.guestsText.textContent = `${total}位房客`; }
          }

          setupGuestCounters() { this.updateGuestsText(); }

          performSearch() {
              const total = Object.values(this.guestCounts).reduce((sum, count) => sum + count, 0);
              if (!this.selectedLocation && total === 0) {
                  alert('请选择目的地或添加房客！');
                  return;
              }
              console.log('执行搜索:', { location: this.selectedLocation || '未选择', guests: this.guestCounts, total: total });
              alert(`搜索条件：\n目的地: ${this.selectedLocation || '未选择'}\n房客总数: ${total}人\n成人: ${this.guestCounts.adults}人\n儿童: ${this.guestCounts.children}人\n婴幼儿: ${this.guestCounts.infants}人`);
          }

          handleSearchItemHover(hoveredItem, isHovering) {
              if (!this.searchBar.classList.contains('expanded')) {
                  if (isHovering) { this.searchBar.classList.add('hovering-item'); }
                  else { this.searchBar.classList.remove('hovering-item'); }
              }
          }

          handleInputChange(value) {
              this.selectedLocation = value;
              this.updateClearButtonVisibility();
          }

          clearLocationInput() {
              this.locationInput.value = '';
              this.selectedLocation = '';
              const locationItem = document.querySelector('[data-type="location"]');
              locationItem.classList.remove('has-content');
              this.updateClearButtonVisibility();
              this.locationInput.focus();
          }

          clearGuestsInput() {
              this.guestsInput.value = '';
              this.selectedProjects = [];
              this.selectedTimeOptions = [];
              this.selectedMonths = [];
              const guestsItem = document.querySelector('[data-type="guests"]');
              guestsItem.classList.remove('has-content');
              this.updateClearButtonVisibility();
              this.guestsInput.focus();

              // 清除所有选中状态
              this.clearAllSelections();
          }

          updateClearButtonVisibility() {
              const locationItem = document.querySelector('[data-type="location"]');
              const guestsItem = document.querySelector('[data-type="guests"]');
              const locationHasContent = this.locationInput && this.locationInput.value.trim();
              const guestsHasContent = this.guestsInput && this.guestsInput.value.trim();
              const isActiveIndicatorShow = this.activeIndicator && this.activeIndicator.classList.contains('show');

              // 处理位置输入框
              if (locationHasContent) {
                  locationItem.classList.add('has-content');
              } else {
                  locationItem.classList.remove('has-content');
              }

              // 处理沟通记录输入框
              if (guestsHasContent) {
                  guestsItem.classList.add('has-content');
              } else {
                  guestsItem.classList.remove('has-content');
              }

              // 位置清除按钮
              const locationClearButton = this.locationClear;
              if (locationClearButton) {
                  if (locationHasContent && isActiveIndicatorShow) {
                      locationClearButton.style.transform = 'translateY(-50%) scale(1)';
                      locationClearButton.style.opacity = '1';
                  } else {
                      locationClearButton.style.transform = 'translateY(-50%) scale(0)';
                      locationClearButton.style.opacity = '0';
                  }
              }

              // 沟通记录清除按钮
              const guestsClearButton = this.guestsClear;
              if (guestsClearButton) {
                  if (guestsHasContent && isActiveIndicatorShow) {
                      guestsClearButton.style.transform = 'translateY(-50%) scale(1)';
                      guestsClearButton.style.opacity = '1';
                  } else {
                      guestsClearButton.style.transform = 'translateY(-50%) scale(0)';
                      guestsClearButton.style.opacity = '0';
                  }
              }
          }
      }

      document.addEventListener('DOMContentLoaded', () => { new AirbnbSearchBox(); });
  </script>
</body>
</html>

