<!DOCTYPE html>
<!-- saved from url=(0194)file:///Users/<USER>/shader-gradient-project/contactNotes%E4%B8%BB%E7%95%8C%E9%9D%A2%E5%90%84%E5%8E%86%E5%8F%B2%E7%89%88%E6%9C%AC/%E6%90%9C%E7%B4%A2%E8%8F%9C%E5%8D%95%E4%BA%A4%E4%BA%92/c.html -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>优化版Airbnb风格搜索框</title>
  <style>
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }

      body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
          background: #fff;
          min-height: 100vh;
          padding: 100px 20px;
      }

      .search-container {
          max-width: 850px;
          margin: 0 auto;
          position: relative;
      }

      .search-bar {
          background: white;
          border-radius: 40px;
          box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05);
          display: flex;
          align-items: center;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          border: 1px solid #ddd;
          position: relative;
          z-index: 1001;
          overflow: visible;
      }

      .search-bar.expanded {
          box-shadow: none;
          background: #ebebeb;
      }

      .active-indicator {
          position: absolute;
          background: white;
          border-radius: 40px;
          box-shadow: rgba(0, 0, 0, 0.1) 0px 3px 12px 0px, rgba(0, 0, 0, 0.08) 0px 1px 2px 0px;
          z-index: 1;
          opacity: 0;
          transform: scale(0.8);
          transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .active-indicator.show {
          opacity: 1;
          transform: scale(1);
      }

      .search-item {
          flex: 1;
          padding: 16px 32px;
          line-height: 32px;
          cursor: pointer;
          position: relative;
          z-index: 2;
          color: #555;
          transition: all 0.3s ease;
          border-radius: 40px;
      }

      .search-item[data-type="guests"] {
          padding-right: 64px; /* Added for button space */
      }

      .search-item:not(.active):hover {
          background: rgba(0,0,0,0.05);
      }

      .search-item.active {
          color: #000;
          background: transparent;
      }

      .search-bar.hovering-item .search-separator {
          opacity: 0;
      }

      .search-placeholder {
          font-size: 14px;
          color: #717171;
          font-weight: 400;
      }

      .search-separator {
          width: 1px;
          height: 32px;
          background-color: #ddd;
          transition: opacity 0.3s ease;
          opacity: 1; /* Explicit default */
      }

      .search-bar.expanded .search-separator {
          opacity: 0;
      }

      .search-item.active .search-placeholder {
          color: #000;
      }

      .search-button {
          background: linear-gradient(135deg, #ff385c, #e31c5f);
          border: none;
          border-radius: 50%;
          width: 48px;
          height: 48px;
          margin: 7px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease, width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), border-radius 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          box-shadow: 0 2px 8px rgba(255, 56, 92, 0.3);
          position: absolute;
          right: 0;
          z-index: 1002;
          padding: 0 16px; /* Add padding for text */
          overflow: hidden; /* Hide overflow during transition */
      }

      .search-button:hover {
          background: linear-gradient(135deg, #e31c5f, #d1185a);
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgba(255, 56, 92, 0.4);
      }

      .search-button.expanded {
          width: 80px; /* Expanded width for text */
          border-radius: 24px; /* Oval shape */
          justify-content: flex-start; /* Align content to start */
          padding-left: 12px; /* Adjust padding for icon */
      }

      .search-icon {
          width: 16px;
          height: 16px;
          fill: white;
          transition: margin-right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .search-button.expanded .search-icon {
          margin-right: 8px; /* Push icon to the left */
      }

      .search-button-text {
          color: white;
          font-size: 16px;
          font-weight: 500;
          white-space: nowrap;
          opacity: 0;
          transform: translateX(10px); /* Start slightly to the right */
          transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .search-button.expanded .search-button-text {
          opacity: 1;
          transform: translateX(0); /* Slide text into view */
      }

      .dropdown-container {
          position: absolute;
          top: calc(100% + 12px);
          background: white;
          border-radius: 32px;
          filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.14));
          opacity: 0;
          visibility: hidden;
          transform: translateY(-10px) scale(0.9);
          transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          z-index: 1000;
          overflow: hidden;
          border: 1px solid rgba(0,0,0,0.08);
          transform-origin: top center;
      }

      .dropdown-container.show {
          opacity: 1;
          visibility: visible;
          transform: translateY(0) scale(1);
      }

      .dropdown-content {
          padding: 32px;
          min-height: 200px;
      }

      .dropdown-section {
          margin-bottom: 32px;
      }

      .dropdown-section:last-child {
          margin-bottom: 0;
      }

      .dropdown-title {
          font-size: 16px;
          font-weight: 600;
          color: #222;
          margin-bottom: 20px;
      }

      .location-item {
          padding: 16px 20px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          margin-bottom: 8px;
          border: 1px solid transparent;
      }

      .location-item:hover {
          background: linear-gradient(135deg, #f8f9ff, #f0f2ff);
          border-color: rgba(103, 126, 234, 0.2);
          transform: translateX(4px);
      }

      .location-name {
          font-size: 15px;
          font-weight: 500;
          color: #222;
          margin-bottom: 2px;
      }

      .location-desc {
          font-size: 13px;
          color: #717171;
      }

      .guest-controls {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20px 0;
          border-bottom: 1px solid #f0f0f0;
      }

      .guest-controls:last-child {
          border-bottom: none;
      }

      .guest-info h4 {
          font-size: 16px;
          font-weight: 500;
          color: #222;
          margin-bottom: 4px;
      }

      .guest-info p {
          font-size: 14px;
          color: #717171;
      }

      .counter-controls {
          display: flex;
          align-items: center;
          gap: 20px;
      }

      .counter-btn {
          width: 36px;
          height: 36px;
          border: 1px solid #b0b0b0;
          border-radius: 50%;
          background: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          color: #717171;
          transition: all 0.2s ease;
          font-weight: 300;
      }

      .counter-btn:hover:not(:disabled) {
          border-color: #222;
          color: #222;
          transform: scale(1.1);
      }

      .counter-btn:active:not(:disabled) {
          transform: scale(0.95);
      }

      .counter-btn:disabled {
          opacity: 0.3;
          cursor: not-allowed;
          transform: none;
      }

      .counter-value {
          font-size: 16px;
          font-weight: 500;
          min-width: 24px;
          text-align: center;
          color: #222;
      }

      .location-item:active {
          transform: translateX(4px) scale(0.98);
      }

      @media (max-width: 768px) {
          .search-container {
              max-width: 100%;
              margin: 0 16px;
          }
          
          .search-item {
              padding: 16px 32px;
          }
          
          .search-item[data-type="guests"] {
              padding-right: 64px; /* Ensure button space */
          }
          
          .dropdown-content {
              padding: 24px;
          }
      }

    
  </style>
  <link rel="stylesheet" href="./c_files/time-picker.css">
</head>
<body>
  <div class="search-container">
      <div class="search-bar hovering-item expanded" id="searchBar">
          <div class="active-indicator show" id="activeIndicator" style="transition: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); left: 0px; width: 407.5px; height: 100%; top: 0px;"></div>
          <div class="search-item active" data-type="location">
              <div class="search-placeholder" id="locationText">电话号码或关键字搜索</div>
          </div>
          <div class="search-separator"></div>
          <div class="search-item" data-type="guests">
              <div class="search-placeholder" id="guestsText">沟通记录</div>
          </div>
          <button class="search-button expanded" id="searchButton">
              <svg class="search-icon" viewBox="0 0 32 32">
                  <path d="M13 24c6.075 0 11-4.925 11-11S19.075 2 13 2 2 6.925 2 13s4.925 11 11 11zm8-3l9 9-3 3-9-9v-2h2z"></path>
              </svg>
              <span class="search-button-text">搜索</span>
          </button>
      </div>

      <div class="dropdown-container show" id="dropdownContainer" style="transition: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); width: 407px; transform-origin: 50% top; left: 1.25px;">
          <div class="dropdown-content" id="dropdownContent">
                  <div class="dropdown-section">
                      <div class="dropdown-title">热门目的地</div>
                      
                          <div class="location-item" data-location="北京">
                              <div class="location-name">北京</div>
                              <div class="location-desc">中国首都，历史文化名城</div>
                          </div>
                      
                          <div class="location-item" data-location="上海">
                              <div class="location-name">上海</div>
                              <div class="location-desc">国际大都市，东方明珠</div>
                          </div>
                      
                          <div class="location-item" data-location="南京">
                              <div class="location-name">南京</div>
                              <div class="location-desc">六朝古都，文化名城</div>
                          </div>
                      
                  </div>
              </div>
      </div>
  </div>

  <script>
      class AirbnbSearchBox {
          constructor() {
              this.currentActiveType = null;
              this.guestCounts = { adults: 0, children: 0, infants: 0 };
              this.selectedLocation = '';
              this.initElements();
              this.bindEvents();
              this.setupGuestCounters();
          }

          initElements() {
              this.searchBar = document.getElementById('searchBar');
              this.activeIndicator = document.getElementById('activeIndicator');
              this.dropdownContainer = document.getElementById('dropdownContainer');
              this.dropdownContent = document.getElementById('dropdownContent');
              this.locationText = document.getElementById('locationText');
              this.guestsText = document.getElementById('guestsText');
              this.searchItems = document.querySelectorAll('.search-item');
              this.searchSeparator = document.querySelector('.search-separator');
              this.searchButton = document.getElementById('searchButton'); 
          }

          bindEvents() {
              this.searchItems.forEach(item => {
                  item.addEventListener('click', (e) => {
                      e.stopPropagation();
                      const type = item.dataset.type;
                      this.showDropdown(type, item);
                  });
                  item.addEventListener('mouseover', (e) => this.handleSearchItemHover(e.currentTarget, true));
                  item.addEventListener('mouseout', (e) => this.handleSearchItemHover(e.currentTarget, false));
              });

              document.addEventListener('click', (e) => {
                  if (!e.target.closest('.search-container')) {
                      this.hideDropdown();
                  }
              });

              document.getElementById('searchButton').addEventListener('click', () => {
                  this.performSearch();
              });
          }

          showDropdown(type, activeItem) {
              if (this.currentActiveType === type) {
                  this.hideDropdown();
                  return;
              }

              const isFirstDisplay = !this.dropdownContainer.classList.contains('show');

              this.currentActiveType = type;
              this.searchItems.forEach(item => item.classList.remove('active'));
              activeItem.classList.add('active');
              this.updateDropdownContent(type);

              if (isFirstDisplay) {
                  this.dropdownContainer.style.transition = 'none';
                  this.activeIndicator.style.transition = 'none';
              }

              this.positionDropdown(activeItem);
              this.updateActiveIndicator(activeItem);

              this.dropdownContainer.offsetHeight;
              this.activeIndicator.offsetHeight;

              if (isFirstDisplay) {
                  this.dropdownContainer.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                  this.activeIndicator.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
              }

              this.searchBar.classList.add('expanded');
              this.dropdownContainer.classList.add('show');
              this.activeIndicator.classList.add('show');
              this.searchButton.classList.add('expanded'); 
          }

          hideDropdown() {
              this.currentActiveType = null;
              this.searchBar.classList.remove('expanded');
              this.searchBar.classList.remove('hovering-item'); 

              this.dropdownContainer.classList.remove('show');
              this.activeIndicator.style.transition = 'none';
              this.activeIndicator.classList.remove('show');
              this.dropdownContainer.style.transition = 'none';
              this.dropdownContainer.classList.remove('show');
              this.activeIndicator.offsetHeight; 
              this.dropdownContainer.offsetHeight; 
              this.activeIndicator.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
              this.dropdownContainer.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
              this.searchItems.forEach(item => item.classList.remove('active'));
              this.searchButton.classList.remove('expanded'); 
          }

          updateActiveIndicator(activeItem) {
              const itemRect = activeItem.getBoundingClientRect();
              const searchBarRect = this.searchBar.getBoundingClientRect();

              const computedStyle = getComputedStyle(this.searchBar);
              const borderLeftWidth = parseFloat(computedStyle.borderLeftWidth);
              const borderTopWidth = parseFloat(computedStyle.borderTopWidth);
              const borderRightWidth = parseFloat(computedStyle.borderRightWidth);

              const left = (itemRect.left - searchBarRect.left) - borderLeftWidth;
              const top = (itemRect.top - searchBarRect.top) - borderTopWidth;
              
              let width = itemRect.width;

              if (activeItem.dataset.type === 'guests') {
                  width = (searchBarRect.width - borderLeftWidth - borderRightWidth) - left;
              }
              
              this.activeIndicator.style.left = left + 'px';
              this.activeIndicator.style.width = width + 'px';
              this.activeIndicator.style.height = '100%'; 
              this.activeIndicator.style.top = top + 'px';
          }

          positionDropdown(activeItem) {
              const searchBarRect = this.searchBar.getBoundingClientRect();
              const itemRect = activeItem.getBoundingClientRect();
              const type = activeItem.dataset.type;
              const containerWidth = type === 'guests' ? 850 : 407;
              const activeItemCenter = (itemRect.left - searchBarRect.left) + (itemRect.width / 2);
              let left = activeItemCenter - (containerWidth / 2);
              const maxLeft = searchBarRect.width - containerWidth;
              left = Math.max(0, Math.min(left, maxLeft));

              this.dropdownContainer.style.width = containerWidth + 'px';
              this.dropdownContainer.style.transformOrigin = `50% top`;
              this.dropdownContainer.style.left = left + 'px';
          }

          updateDropdownContent(type) {
              if (type === 'location') {
                  this.dropdownContent.innerHTML = this.getLocationContent();
                  this.bindLocationEvents();
              } else if (type === 'guests') {
                  this.dropdownContent.innerHTML = this.getTimePickerContent();
                  this.bindGuestEvents();
              }
          }

          getTimePickerContent() {
            return `
                <div class="container" style="max-width: none; padding: 0; margin: 0;">
                    <div class="dropdown" style="border: none; box-shadow: none; border-radius: 0;">
                        <div class="dropdown-header">
                            <div class="tabs-container">
                                <button class="tab active" id="tab-projects">指定项目</button>
                                <button class="tab" id="tab-timeline">最近沟通</button>
                            </div>
                        </div>
                        <div class="dropdown-content">
                            <div class="projects-view" id="projects-view">
                                <h3>选择相关WorkFeed</h3>
                                <div class="projects-grid">
                                    <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月7日</div></div>
                                    <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月7日</div></div>
                                    <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月7日</div></div>
                                    <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月8日</div></div>
                                    <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月8日</div></div>
                                    <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月8日</div></div>
                                </div>
                            </div>
                            <div class="timeline-view hidden" id="timeline-view">
                                <h3>您多久前沟通过？</h3>
                                <div class="time-options">
                                    <button class="time-option">本周</button>
                                    <button class="time-option active">上月</button>
                                    <button class="time-option">近两周</button>
                                </div>
                                <div class="timeline-question">您上次在几月份沟通过？</div>
                                <div class="months-container">
                                    <button class="nav-button" id="prev-months"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m15 18-6-6 6-6"/></svg></button>
                                    <div class="months-grid" id="months-grid">
                                        <div class="month-card"><svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg><div class="month-name">3月</div><div class="month-year">2025</div></div>
                                        <div class="month-card"><svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg><div class="month-name">4月</div><div class="month-year">2025</div></div>
                                        <div class="month-card"><svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg><div class="month-name">5月</div><div class="month-year">2025</div></div>
                                        <div class="month-card"><svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg><div class="month-name">6月</div><div class="month-year">2025</div></div>
                                        <div class="month-card"><svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg><div class="month-name">7月</div><div class="month-year">2025</div></div>
                                        <div class="month-card"><svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg><div class="month-name">8月</div><div class="month-year">2025</div></div>
                                    </div>
                                    <button class="nav-button" id="next-months"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m9 18 6-6-6-6"/></svg></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
          }

          getLocationContent() {
              const locations = [
                  { name: '北京', desc: '中国首都，历史文化名城' },
                  { name: '上海', desc: '国际大都市，东方明珠' },
                  { name: '南京', desc: '六朝古都，文化名城' }
              ];

              return `
                  <div class="dropdown-section">
                      <div class="dropdown-title">热门目的地</div>
                      ${locations.map(location => `
                          <div class="location-item" data-location="${location.name}">
                              <div class="location-name">${location.name}</div>
                              <div class="location-desc">${location.desc}</div>
                          </div>
                      `).join('')}
                  </div>
              `;
          }

          bindLocationEvents() {
              const locationItems = this.dropdownContent.querySelectorAll('.location-item');
              locationItems.forEach(item => {
                  item.addEventListener('click', () => {
                      const location = item.dataset.location;
                      this.selectedLocation = location;
                      this.locationText.textContent = location;
                      this.hideDropdown();
                  });
              });
          }

          bindGuestEvents() {
            const tabProjects = document.getElementById('tab-projects');
            const tabTimeline = document.getElementById('tab-timeline');
            const projectsView = document.getElementById('projects-view');
            const timelineView = document.getElementById('timeline-view');
            const projectCards = document.querySelectorAll('.project-card');
            const timeOptions = document.querySelectorAll('.time-option');
            const monthCards = document.querySelectorAll('.month-card');
            const prevMonthsButton = document.getElementById('prev-months');
            const nextMonthsButton = document.getElementById('next-months');
            const monthsGrid = document.getElementById('months-grid');

            const defaultIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';
            const selectedIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v16.07a2 2 0 0 1-.46 1.28l-.12.13L21 29.75a2 2 0 0 1-1.24.58H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h11.18v-5.66a5 5 0 0 1 4.78-5h5.88zm-.08 8h-5.58a3 3 0 0 0-3 2.82v5.76zm-18.58-16h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';

            tabProjects.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
                this.classList.add('active');
                projectsView.classList.remove('hidden');
                timelineView.classList.add('hidden');
            });

            tabTimeline.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
                this.classList.add('active');
                timelineView.classList.remove('hidden');
                projectsView.classList.add('hidden');
            });

            function clearTimeOptions() {
                timeOptions.forEach(opt => opt.classList.remove('active'));
            }

            function clearMonthCards() {
                monthCards.forEach(c => {
                    c.classList.remove('active');
                    const icon = c.querySelector('.month-icon');
                    if (icon) {
                        icon.innerHTML = defaultIconPath;
                        icon.style.fill = 'var(--linaria-theme_palette-foggy)';
                    }
                });
            }

            function clearProjectCards() {
                projectCards.forEach(p => p.classList.remove('active'));
            }

            timeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    clearMonthCards();
                    clearProjectCards();
                    timeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            monthCards.forEach(card => {
                card.addEventListener('click', function() {
                    clearTimeOptions();
                    clearProjectCards();
                    this.classList.toggle('active');
                    const icon = this.querySelector('.month-icon');
                    if (this.classList.contains('active')) {
                        if (icon) {
                            icon.innerHTML = selectedIconPath;
                            icon.style.fill = 'var(--linaria-theme_palette-hof)';
                        }
                    } else {
                        if (icon) {
                            icon.innerHTML = defaultIconPath;
                            icon.style.fill = 'var(--linaria-theme_palette-foggy)';
                        }
                    }
                });
            });

            projectCards.forEach(card => {
                card.addEventListener('click', function() {
                    clearTimeOptions();
                    clearMonthCards();
                    this.classList.toggle('active');
                });
            });

            prevMonthsButton.addEventListener('click', () => {
                monthsGrid.scrollBy({ left: -200, behavior: 'smooth' });
            });

            nextMonthsButton.addEventListener('click', () => {
                monthsGrid.scrollBy({ left: 200, behavior: 'smooth' });
            });
          }

          changeGuestCount(type, change) {
              const newCount = Math.max(0, this.guestCounts[type] + change);
              this.guestCounts[type] = newCount;
              document.getElementById(type + 'Count').textContent = newCount;
              this.updateGuestsText();
              this.updateCounterStates();
          }

          updateCounterStates() {
              Object.keys(this.guestCounts).forEach(type => {
                  const minusBtn = this.dropdownContent.querySelector(`[data-type="${type}"][data-action="minus"]`);
                  if (minusBtn) {
                      minusBtn.disabled = this.guestCounts[type] === 0;
                  }
              });
          }

          updateGuestsText() {
              const total = Object.values(this.guestCounts).reduce((sum, count) => sum + count, 0);
              if (total === 0) {
                  this.guestsText.textContent = '沟通记录';
              } else {
                  this.guestsText.textContent = `${total}位房客`;
              }
          }

          setupGuestCounters() {
              this.updateGuestsText();
          }

          performSearch() {
              const total = Object.values(this.guestCounts).reduce((sum, count) => sum + count, 0);
              if (!this.selectedLocation && total === 0) {
                  alert('请选择目的地或添加房客！');
                  return;
              }
              console.log('执行搜索:', {
                  location: this.selectedLocation || '未选择',
                  guests: this.guestCounts,
                  total: total
              });
              alert(`搜索条件：\n目的地: ${this.selectedLocation || '未选择'}\n房客总数: ${total}人\n成人: ${this.guestCounts.adults}人\n儿童: ${this.guestCounts.children}人\n婴幼儿: ${this.guestCounts.infants}人`);
          }

          handleSearchItemHover(hoveredItem, isHovering) {
              // Only apply hover effect if the search bar is not currently expanded (i.e., no item is active)
              if (!this.searchBar.classList.contains('expanded')) {
                  if (isHovering) {
                      this.searchBar.classList.add('hovering-item');
                  } else {
                      this.searchBar.classList.remove('hovering-item');
                  }
              }
          }
      }

      document.addEventListener('DOMContentLoaded', () => {
          new AirbnbSearchBox();
      });
  </script>

</body></html>