<!DOCTYPE html>
<!-- saved from url=(0213)file:///Users/<USER>/shader-gradient-project/contactNotes%E4%B8%BB%E7%95%8C%E9%9D%A2%E5%90%84%E5%8E%86%E5%8F%B2%E7%89%88%E6%9C%AC/%E6%90%9C%E7%B4%A2%E8%8F%9C%E5%8D%95%E4%BA%A4%E4%BA%92/optimized-search-box.html -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>集成版搜索框（优化版）</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif; background: #fff; min-height: 100vh; padding: 100px 20px; }
    .search-container { max-width: 850px; margin: 0 auto; position: relative; }
    .search-bar { background: white; border-radius: 40px; box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05); display: flex; align-items: center; transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); border: 1px solid #ddd; position: relative; z-index: 1001; overflow: visible; }
    .search-bar.expanded { box-shadow: none; background: #ebebeb; }
    .active-indicator { position: absolute; background: white; border-radius: 40px; box-shadow: rgba(0, 0, 0, 0.1) 0px 3px 12px 0px, rgba(0, 0, 0, 0.08) 0px 1px 2px 0px; z-index: 1; opacity: 0; transform: scale(0.6); transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
    .active-indicator.show { opacity: 1; transform: scale(1); }
    .search-item { flex: 1; padding: 16px 32px; line-height: 32px; cursor: pointer; position: relative; z-index: 2; color: #555; transition: all 0.3s ease; border-radius: 40px; }
    .search-item[data-type="guests"] { padding-right: 64px; }
    .search-item:not(.active):hover { background: rgba(0,0,0,0.05); }
    .search-item.active { color: #000; background: transparent; }
    .search-bar.hovering-item .search-separator { opacity: 0; }
    .search-placeholder { font-size: 14px; color: #717171; font-weight: 400; }
    .search-separator { width: 1px; height: 32px; background-color: #ddd; transition: opacity 0.3s ease; opacity: 1; }
    .search-bar.expanded .search-separator { opacity: 0; }
    .search-item.active .search-placeholder { color: #000; }
    .search-button { background: linear-gradient(135deg, #ff385c, #e31c5f); border: none; border-radius: 50%; width: 48px; height: 48px; margin: 7px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease, width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), border-radius 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); box-shadow: 0 2px 8px rgba(255, 56, 92, 0.3); position: absolute; right: 0; z-index: 1002; padding: 0 16px; overflow: hidden; }
    .search-button:hover { background: linear-gradient(135deg, #e31c5f, #d1185a); transform: scale(1.05); box-shadow: 0 4px 12px rgba(255, 56, 92, 0.4); }
    .search-button.expanded { width: 80px; border-radius: 24px; justify-content: flex-start; padding-left: 12px; }
    .search-icon { width: 16px; height: 16px; fill: white; transition: margin-right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
    .search-button.expanded .search-icon { margin-right: 8px; }
    .search-button-text { color: white; font-size: 16px; font-weight: 500; white-space: nowrap; opacity: 0; transform: translateX(10px); transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
    .search-button.expanded .search-button-text { opacity: 1; transform: translateX(0); }
    .dropdown-container { position: absolute; top: calc(100% + 12px); background: white; border-radius: 32px; filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.14)); opacity: 0; visibility: hidden; transform: scale(0.5); transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1), opacity 0.5s cubic-bezier(0.175, 0.885, 0.32, 1), visibility 0s linear 0.5s, left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), width 0.6s cubic-bezier(0.175, 0.885, 0.32, 1), height 0.6s cubic-bezier(0.175, 0.885, 0.32, 1); z-index: 1000; overflow: hidden; border: 1px solid rgba(0,0,0,0.08); transform-origin: top center; }
    .dropdown-container.show { opacity: 1; visibility: visible; transform: translateY(0) scale(1); }
    .dropdown-content { padding: 42px 32px 30px; min-height: 200px; }
    .dropdown-section { margin-bottom: 32px; }
    .dropdown-section:last-child { margin-bottom: 0; }
    .dropdown-title { font-size: 16px; font-weight: 600; color: #222; margin-bottom: 20px; }
    .location-item { padding: 16px 20px; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; margin-bottom: 8px; border: 1px solid transparent; }
    .location-item:hover { background: linear-gradient(135deg, #f8f9ff, #f0f2ff); border-color: rgba(103, 126, 234, 0.2); transform: translateX(4px); }
    .location-name { font-size: 15px; font-weight: 500; color: #222; margin-bottom: 2px; }
    .location-desc { font-size: 13px; color: #717171; }
    .location-item:active { transform: translateX(4px) scale(0.98); }
    
    /* 时间选择器样式 */
    .container { max-width: none; padding: 0; margin: 0; }
    .dropdown { border: none; box-shadow: none; border-radius: 0; }
    .dropdown-header { margin-bottom: 20px; }
    .tabs-container { display: flex; gap: 8px; }
    .tab { background: transparent; border: none; padding: 8px 16px; border-radius: 8px; cursor: pointer; font-size: 14px; color: #717171; transition: all 0.2s; }
    .tab.active { background: #f0f0f0; color: #222; font-weight: 500; }
    .projects-view, .timeline-view { animation: fadeIn 0.3s ease; }
    .hidden { display: none !important; }
    .projects-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-top: 16px; }
    .project-card { padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s; }
    .project-card:hover { border-color: #222; background: #fafafa; }
    .project-card.active { border-color: #222; background: #f0f0f0; }
    .project-name { font-size: 14px; color: #222; margin-bottom: 4px; }
    .project-date { font-size: 12px; color: #717171; }
    .time-options { display: flex; gap: 8px; margin: 16px 0; }
    .time-option { padding: 8px 16px; border: 1px solid #e0e0e0; border-radius: 20px; background: white; cursor: pointer; font-size: 14px; transition: all 0.2s; }
    .time-option:hover { border-color: #222; }
    .time-option.active { background: #222; color: white; border-color: #222; }
    .timeline-question { font-size: 14px; color: #717171; margin: 20px 0 16px; }
    .months-container { display: flex; align-items: center; gap: 16px; position: relative; }
    .nav-button { background: white; border: 1px solid #e0e0e0; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s; flex-shrink: 0; }
    .nav-button:hover { border-color: #222; background: #fafafa; }
    .months-grid { display: flex; gap: 12px; overflow-x: auto; scroll-behavior: smooth; flex: 1; scrollbar-width: none; }
    .months-grid::-webkit-scrollbar { display: none; }
    .month-card { min-width: 100px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; text-align: center; cursor: pointer; transition: all 0.2s; }
    .month-card:hover { border-color: #222; background: #fafafa; }
    .month-card.active { border-color: #222; background: #f0f0f0; }
    .month-icon { margin: 0 auto 8px; }
    .month-name { font-size: 14px; color: #222; margin-bottom: 2px; }
    .month-year { font-size: 12px; color: #717171; }
    
    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    @media (max-width: 768px) { 
      .search-container { max-width: 100%; margin: 0 16px; }
      .projects-grid { grid-template-columns: repeat(2, 1fr); }
    }
  </style>
    <link rel="stylesheet" href="./集成版搜索框（优化版）_files/time-picker.css">
</head>
<body>
  <div class="search-container">
    <div class="search-bar expanded" id="searchBar">
      <div class="active-indicator show" id="activeIndicator" style="left: 0px; width: 407.5px; height: 100%; top: 0px;"></div>
      <div class="search-item active" data-type="location">
        <div class="search-placeholder" id="locationText">电话号码或关键字搜索</div>
      </div>
      <div class="search-separator"></div>
      <div class="search-item" data-type="guests">
        <div class="search-placeholder" id="guestsText">沟通记录</div>
      </div>
      <button class="search-button expanded" id="searchButton">
        <svg class="search-icon" viewBox="0 0 32 32">
          <path d="M13 24c6.075 0 11-4.925 11-11S19.075 2 13 2 2 6.925 2 13s4.925 11 11 11zm8-3l9 9-3 3-9-9v-2h2z"></path>
        </svg>
        <span class="search-button-text">搜索</span>
      </button>
    </div>
    <div class="dropdown-container show" id="dropdownContainer" style="left: 1.25px; transform-origin: 50% top; width: 407px; opacity: 1; transform: scale(1); visibility: visible; transition: width 0.6s cubic-bezier(0.175, 0.885, 0.32, 1), height 0.6s cubic-bezier(0.175, 0.885, 0.32, 1); height: 367px;">
      <div class="dropdown-content" id="dropdownContent"><div class="dropdown-section">
          <div class="dropdown-title">热门目的地</div>
          
            <div class="location-item" data-location="北京">
              <div class="location-name">北京</div>
              <div class="location-desc">中国首都，历史文化名城</div>
            </div>
          
            <div class="location-item" data-location="上海">
              <div class="location-name">上海</div>
              <div class="location-desc">国际大都市，东方明珠</div>
            </div>
          
            <div class="location-item" data-location="南京">
              <div class="location-name">南京</div>
              <div class="location-desc">六朝古都，文化名城</div>
            </div>
          
        </div></div>
    </div>
  </div>

  <script>
    class SearchBox {
      constructor() {
        this.state = {
          activeType: null,
          location: '',
          selectedProjects: new Set(),
          selectedMonths: new Set(),
          selectedTimeOption: null
        };
        
        this.cache = {
          elements: {},
          dimensions: new WeakMap()
        };
        
        this.init();
      }

      init() {
        this.cacheElements();
        this.bindEvents();
      }

      cacheElements() {
        const ids = ['searchBar', 'activeIndicator', 'dropdownContainer', 'dropdownContent', 
                     'locationText', 'guestsText', 'searchButton'];
        ids.forEach(id => this.cache.elements[id] = document.getElementById(id));
        this.cache.elements.searchItems = document.querySelectorAll('.search-item');
      }

      bindEvents() {
        const { searchItems, searchButton } = this.cache.elements;
        
        // 使用事件委托优化
        this.cache.elements.searchBar.addEventListener('click', e => {
          const item = e.target.closest('.search-item');
          if (item) {
            e.stopPropagation();
            this.showDropdown(item.dataset.type, item);
          }
        });

        // 优化 hover 事件
        let hoverTimeout;
        this.cache.elements.searchBar.addEventListener('mouseover', e => {
          const item = e.target.closest('.search-item');
          if (item && !this.cache.elements.searchBar.classList.contains('expanded')) {
            clearTimeout(hoverTimeout);
            this.cache.elements.searchBar.classList.add('hovering-item');
          }
        });

        this.cache.elements.searchBar.addEventListener('mouseout', e => {
          if (!e.currentTarget.contains(e.relatedTarget)) {
            hoverTimeout = setTimeout(() => {
              this.cache.elements.searchBar.classList.remove('hovering-item');
            }, 100);
          }
        });

        searchButton.addEventListener('click', e => {
          e.stopPropagation();
          this.performSearch();
        });

        // 全局点击关闭
        document.addEventListener('click', e => {
          if (!e.target.closest('.search-container')) {
            this.hideDropdown();
          }
        });
      }

      showDropdown(type, activeItem) {
        if (this.state.activeType === type) return;

        const isFirst = !this.cache.elements.dropdownContainer.classList.contains('show');
        const isSwitch = this.state.activeType && this.state.activeType !== type;
        
        // FLIP 动画优化
        const flipData = isSwitch ? this.captureFlipState() : null;
        
        this.state.activeType = type;
        this.updateActiveState(activeItem);
        
        if (isSwitch && flipData) {
          this.animateFlip(flipData, type);
        } else {
          this.updateDropdownContent(type);
          this.positionDropdown(activeItem, type);
          this.animateDropdown(isFirst);
        }
        
        this.setExpandedState(true);
      }

      captureFlipState() {
        const rect = this.cache.elements.dropdownContainer.getBoundingClientRect();
        const barRect = this.cache.elements.searchBar.getBoundingClientRect();
        return {
          width: rect.width,
          height: rect.height,
          left: rect.left - barRect.left
        };
      }

      animateFlip(flipData, type) {
        const container = this.cache.elements.dropdownContainer;
        
        // 准备新内容
        container.style.visibility = 'hidden';
        this.updateDropdownContent(type);
        
        // 计算新尺寸
        const newWidth = type === 'guests' ? 850 : 407;
        container.style.width = `${newWidth}px`;
        container.style.height = 'auto';
        const newHeight = container.offsetHeight;
        
        // FLIP: 从旧状态开始
        Object.assign(container.style, {
          left: `${flipData.left}px`,
          width: `${flipData.width}px`,
          height: `${flipData.height}px`,
          visibility: 'visible',
          transition: 'width 0.6s cubic-bezier(0.175, 0.885, 0.32, 1), height 0.6s cubic-bezier(0.175, 0.885, 0.32, 1)'
        });
        
        // 触发动画
        requestAnimationFrame(() => {
          container.style.width = `${newWidth}px`;
          container.style.height = `${newHeight}px`;
        });
      }

      updateActiveState(activeItem) {
        this.cache.elements.searchItems.forEach(item => 
          item.classList.toggle('active', item === activeItem)
        );
        this.updateActiveIndicator(activeItem);
      }

      updateActiveIndicator(activeItem) {
        const itemRect = activeItem.getBoundingClientRect();
        const barRect = this.cache.elements.searchBar.getBoundingClientRect();
        const style = getComputedStyle(this.cache.elements.searchBar);
        const borderLeft = parseFloat(style.borderLeftWidth);
        
        const left = itemRect.left - barRect.left - borderLeft;
        const width = activeItem.dataset.type === 'guests' 
          ? barRect.width - borderLeft - parseFloat(style.borderRightWidth) - left
          : itemRect.width;
        
        Object.assign(this.cache.elements.activeIndicator.style, {
          left: `${left}px`,
          width: `${width}px`,
          height: '100%',
          top: '0'
        });
      }

      positionDropdown(activeItem, type) {
        const width = type === 'guests' ? 850 : 407;
        const container = this.cache.elements.dropdownContainer;
        
        if (type === 'location') {
          const barRect = this.cache.elements.searchBar.getBoundingClientRect();
          const itemRect = activeItem.getBoundingClientRect();
          const center = (itemRect.left - barRect.left) + (itemRect.width / 2);
          const left = Math.max(0, Math.min(center - width / 2, barRect.width - width));
          
          container.style.left = `${left}px`;
          container.style.transformOrigin = '50% top';
        } else {
          container.style.left = '0px';
          container.style.transformOrigin = 'top left';
        }
        
        container.style.width = `${width}px`;
      }

      animateDropdown(isFirst) {
        const container = this.cache.elements.dropdownContainer;
        const indicator = this.cache.elements.activeIndicator;
        
        if (isFirst) {
          // 初始动画状态
          Object.assign(container.style, {
            opacity: '0.2',
            transform: 'scale(0.5)',
            visibility: 'visible',
            transition: 'transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1), opacity 0.5s cubic-bezier(0.175, 0.885, 0.32, 1)'
          });
          
          requestAnimationFrame(() => {
            container.style.opacity = '1';
            container.style.transform = 'scale(1)';
          });
        }
      }

      setExpandedState(expanded) {
        const elements = [this.cache.elements.searchBar, this.cache.elements.dropdownContainer, 
                         this.cache.elements.activeIndicator, this.cache.elements.searchButton];
        const action = expanded ? 'add' : 'remove';
        
        elements.forEach(el => {
          if (el === this.cache.elements.dropdownContainer || el === this.cache.elements.activeIndicator) {
            el.classList[action]('show');
          }
          if (el === this.cache.elements.searchBar || el === this.cache.elements.searchButton) {
            el.classList[action]('expanded');
          }
        });
      }

      hideDropdown() {
        if (!this.state.activeType) return;
        
        this.state.activeType = null;
        
        // 立即隐藏（无动画）
        const container = this.cache.elements.dropdownContainer;
        const indicator = this.cache.elements.activeIndicator;
        
        [container, indicator].forEach(el => {
          if (!el) return;
          // 先取消过渡，确保没有动画
          el.style.transition = 'none';
          // 立即设置为不可见状态
          el.style.opacity = '0';
          el.style.visibility = 'hidden';
          el.style.transform = 'scale(0.5)';
        });
        
        this.setExpandedState(false);
        this.cache.elements.searchItems.forEach(item => item.classList.remove('active'));
        
        // 清理内联样式并移除 show 类，恢复到初始状态（下一次打开可以重新应用打开动画）
        requestAnimationFrame(() => {
          [container, indicator].forEach(el => {
            if (!el) return;
            el.style.cssText = '';
            el.classList.remove('show');
          });
        });
      }

      updateDropdownContent(type) {
        const content = type === 'location' ? this.getLocationContent() : this.getGuestsContent();
        this.cache.elements.dropdownContent.innerHTML = content;
        
        // 延迟绑定事件，避免阻塞渲染
        requestAnimationFrame(() => {
          type === 'location' ? this.bindLocationEvents() : this.bindGuestsEvents();
        });
      }

      getLocationContent() {
        const locations = [
          { name: '北京', desc: '中国首都，历史文化名城' },
          { name: '上海', desc: '国际大都市，东方明珠' },
          { name: '南京', desc: '六朝古都，文化名城' }
        ];
        
        return `<div class="dropdown-section">
          <div class="dropdown-title">热门目的地</div>
          ${locations.map(loc => `
            <div class="location-item" data-location="${loc.name}">
              <div class="location-name">${loc.name}</div>
              <div class="location-desc">${loc.desc}</div>
            </div>
          `).join('')}
        </div>`;
      }

      getGuestsContent() {
        const months = ['3月', '4月', '5月', '6月', '7月', '8月'];
        const projects = Array(6).fill().map((_, i) => ({
          name: '项目名称1',
          date: `8月${i < 3 ? 7 : 8}日`
        }));
        
        return `
          <div class="container">
            <div class="dropdown">
              <div class="dropdown-header">
                <div class="tabs-container">
                  <button class="tab active" data-tab="projects">WorkFeed</button>
                  <button class="tab" data-tab="timeline">最近沟通</button>
                </div>
              </div>
              <div class="dropdown-content">
                <div class="projects-view" data-view="projects">
                  <h3>选择相关WorkFeed</h3>
                  <div class="projects-grid">
                    ${projects.map((p, i) => `
                      <div class="project-card" data-id="${i}">
                        <div class="project-name">${p.name}</div>
                        <div class="project-date">${p.date}</div>
                      </div>
                    `).join('')}
                  </div>
                </div>
                <div class="timeline-view hidden" data-view="timeline">
                  <h3>您多久前沟通的？</h3>
                  <div class="time-options">
                    <button class="time-option">本周</button>
                    <button class="time-option active">上月</button>
                    <button class="time-option">近两周</button>
                  </div>
                  <div class="timeline-question">您上次是几月沟通的？</div>
                  <div class="months-container">
                    <button class="nav-button" data-nav="prev">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" style="width:12px;height:12px;stroke:currentcolor;stroke-width:5.33;fill:none">
                        <path d="M20 28 8.7 16.7a1 1 0 0 1 0-1.4L20 4"/>
                      </svg>
                    </button>
                    <div class="months-grid">
                      ${months.map(m => `
                        <div class="month-card" data-month="${m}">
                          <svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" style="width:32px;height:32px;fill:#6a6a6a">
                            <path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"/>
                          </svg>
                          <div class="month-name">${m}</div>
                          <div class="month-year">2025</div>
                        </div>
                      `).join('')}
                    </div>
                    <button class="nav-button" data-nav="next">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" style="width:12px;height:12px;stroke:currentcolor;stroke-width:5.33;fill:none">
                        <path d="m12 4 11.3 11.3a1 1 0 0 1 0 1.4L12 28"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>`;
      }

      bindLocationEvents() {
        this.cache.elements.dropdownContent.addEventListener('click', e => {
          const item = e.target.closest('.location-item');
          if (item) {
            this.state.location = item.dataset.location;
            this.cache.elements.locationText.textContent = this.state.location;
            this.hideDropdown();
          }
        });
      }

      bindGuestsEvents() {
        const content = this.cache.elements.dropdownContent;
        
        // 标签切换
        content.addEventListener('click', e => {
          const tab = e.target.closest('.tab');
          if (tab) {
            this.switchTab(tab.dataset.tab);
            return;
          }
          
          // 项目/时间/月份选择
          const card = e.target.closest('.project-card, .time-option, .month-card');
          if (card) {
            this.toggleSelection(card);
            return;
          }
          
          // 导航按钮
          const nav = e.target.closest('.nav-button');
          if (nav) {
            const grid = content.querySelector('.months-grid');
            grid.scrollBy({ left: nav.dataset.nav === 'prev' ? -200 : 200, behavior: 'smooth' });
          }
        });
      }

      switchTab(tabName) {
        const content = this.cache.elements.dropdownContent;
        const tabs = content.querySelectorAll('.tab');
        const views = content.querySelectorAll('[data-view]');
        
        tabs.forEach(t => t.classList.toggle('active', t.dataset.tab === tabName));
        views.forEach(v => v.classList.toggle('hidden', v.dataset.view !== tabName));
        
        // 动画高度变化
        const container = content.querySelector('.dropdown-content');
        const currentHeight = container.offsetHeight;
        container.style.height = `${currentHeight}px`;
        
        requestAnimationFrame(() => {
          container.style.transition = 'height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
          container.style.height = 'auto';
          const newHeight = container.offsetHeight;
          container.style.height = `${currentHeight}px`;
          
          requestAnimationFrame(() => {
            container.style.height = `${newHeight}px`;
            setTimeout(() => {
              container.style.height = '';
              container.style.transition = '';
            }, 300);
          });
        });
      }

      toggleSelection(element) {
        const type = element.className;
        
        // 清除其他类型的选择
        if (type.includes('project')) {
          this.clearSelections('.time-option, .month-card');
          this.state.selectedTimeOption = null;
          this.state.selectedMonths.clear();
        } else if (type.includes('time')) {
          this.clearSelections('.project-card, .month-card');
          this.state.selectedProjects.clear();
          this.state.selectedMonths.clear();
        } else if (type.includes('month')) {
          this.clearSelections('.project-card, .time-option');
          this.state.selectedProjects.clear();
          this.state.selectedTimeOption = null;
        }
        
        // 切换当前选择
        element.classList.toggle('active');
        
        // 更新状态
        if (type.includes('project')) {
          const id = element.dataset.id;
          this.state.selectedProjects[element.classList.contains('active') ? 'add' : 'delete'](id);
        } else if (type.includes('month')) {
          const month = element.dataset.month;
          this.state.selectedMonths[element.classList.contains('active') ? 'add' : 'delete'](month);
          
          // 更新图标
          const icon = element.querySelector('.month-icon');
          if (icon) {
            const isActive = element.classList.contains('active');
            icon.style.fill = isActive ? '#222' : '#6a6a6a';
            icon.innerHTML = isActive 
              ? '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v16.07a2 2 0 0 1-.46 1.28l-.12.13L21 29.75a2 2 0 0 1-1.24.58H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h11.18v-5.66a5 5 0 0 1 4.78-5h5.88zm-.08 8h-5.58a3 3 0 0 0-3 2.82v5.76zm-18.58-16h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"/>'
              : '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"/>';
          }
        } else if (type.includes('time')) {
          this.clearSelections('.time-option');
          element.classList.add('active');
          this.state.selectedTimeOption = element.textContent;
        }
      }

      clearSelections(selector) {
        this.cache.elements.dropdownContent.querySelectorAll(selector).forEach(el => {
          el.classList.remove('active');
          
          // 重置月份图标
          if (el.classList.contains('month-card')) {
            const icon = el.querySelector('.month-icon');
            if (icon) {
              icon.style.fill = '#6a6a6a';
              icon.innerHTML = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"/>';
            }
          }
        });
      }

      performSearch() {
        const hasLocation = this.state.location;
        const hasSelection = this.state.selectedProjects.size > 0 || 
                           this.state.selectedMonths.size > 0 || 
                           this.state.selectedTimeOption;
        
        if (!hasLocation && !hasSelection) {
          alert('请选择搜索条件！');
          return;
        }
        
        // 构建搜索参数
        const searchParams = {
          location: this.state.location,
          projects: Array.from(this.state.selectedProjects),
          months: Array.from(this.state.selectedMonths),
          timeRange: this.state.selectedTimeOption
        };
        
        // 执行搜索
        console.log('搜索参数:', searchParams);
        alert(`搜索条件：\n位置: ${searchParams.location || '未选择'}\n项目: ${searchParams.projects.length}个\n月份: ${searchParams.months.join(', ') || '未选择'}\n时间范围: ${searchParams.timeRange || '未选择'}`);
      }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => new SearchBox());
  </script>

</body></html>