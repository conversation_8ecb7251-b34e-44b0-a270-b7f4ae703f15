<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间选择器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Circular, -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif;
            background-color: #f7f7f7;
            padding: 32px 16px;
        }

        .container {
            max-width: 780px;
            margin: 0 auto;
        }

        .dropdown {
            background: white;
            border-radius: 16px;
            box-shadow: 0 6px 16px rgba(0,0,0,0.12);
            overflow: hidden;
            border: 1px solid #dddddd;
        }

        .dropdown-header {
            display: flex;
            justify-content: center;
            padding: 24px;
        }

        .tabs-container {
            display: flex;
            background: #d0d0d0;
            border-radius: 32px;
            padding: 4px;
            gap: 5px;
        }

        .tab {
            padding: 12px 24px;
            border-radius: 28px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            color: #666666;
            background: transparent;
            border: none;
            transition: all 0.2s ease;
            font-family: inherit;
            white-space: nowrap;
        }

        .tab:hover {
            background: rgba(255, 255, 255, 0.5);
            color: #333333;
        }

        .tab.active {
            background: white;
            color: #222222;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
        }

        .tab.active:hover {
            background: white;
            color: #222222;
        }

        .dropdown-content {
            padding: 32px;
            background: white;
        }

        .projects-view h3 {
            font-size: 22px;
            font-weight: 600;
            color: #222222;
            text-align: center;
            margin-bottom: 32px;
            line-height: 1.25;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 16px;
        }

        .project-card {
            background: white;
            border: 1px solid #dddddd;
            border-radius: 12px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .project-card:hover {
            border-color: #222222;
            box-shadow: 0 6px 16px rgba(0,0,0,0.12);
        }

        .project-card.active {
            background: #f7f7f7;
            border: 2px solid #222222;
        }

        

        .project-name {
            font-weight: 600;
            color: #222222;
            margin-bottom: 8px;
            font-size: 16px;
            line-height: 1.25;
        }

        .project-date {
            color: #717171;
            font-size: 14px;
            line-height: 1.43;
        }

        .timeline-view h3 {
            font-size: 22px;
            font-weight: 600;
            color: #222222;
            text-align: center;
            margin-bottom: 32px;
            line-height: 1.25;
        }

        .time-options {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 48px;
        }

        .time-option {
            padding: 13px 23px;
            border-radius: 32px;
            background: white;
            border: 1px solid #dddddd;
            cursor: pointer;
            transition: all 0.25s ease;
            font-weight: 600;
            font-size: 14px;
            color: #222222;
            font-family: inherit;
        }

        .time-option:hover {
            border-color: #717171;
        }

        .time-option.active {
            background: #f7f7f7;
            border: 1px solid #222222;
            color: #222222;
        }

        .timeline-question {
            font-size: 22px;
            font-weight: 600;
            color: #222222;
            text-align: center;
            margin-bottom: 32px;
            line-height: 1.25;
        }

        .months-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .nav-button {
            background: white;
            border: 1px solid #e0e0e0;
            cursor: pointer;
            padding: 12px;
            border-radius: 50%;
            transition: all 0.2s ease;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            position: relative;
            z-index: 1;
        }

        .nav-button:hover {
            border-color: #cccccc;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        #prev-months {
            margin-right: -22px;
        }

        #next-months {
            margin-left: -22px;
        }

        .months-grid {
            display: flex;
            gap: 12px;
        }

        .month-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 16px;
            background: white;
            border: 2px solid #dddddd;
            border-radius: 20px;
            cursor: pointer;
            transition: border-color 0.25s ease, box-shadow 0.25s ease, transform 0.1s ease;
            min-width: 100px;
            height: 120px;
            justify-content: center;
        }

        .month-card:hover {
            border-color: #222222;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .month-card.active {
            background: #f7f7f7;
            border: 3px solid #222222;
        }

        .month-card.active:hover {
            background: #f7f7f7;
            border: 3px solid #222222;
        }

        .month-icon {
            width: 32px;
            height: 32px;
            margin-bottom: 12px;
            opacity: 0.7;
        }

        .month-card.active .month-icon {
            opacity: 1;
        }

        .month-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 4px;
            line-height: 1.25;
            color: #222222;
        }

        .month-year {
            font-size: 14px;
            opacity: 0.7;
            line-height: 1.43;
            color: #717171;
        }

        .hidden {
            display: none;
        }

        .tab:active,
        .time-option:active,
        .nav-button:active {
            transform: scale(0.96);
            transition: transform 0.1s ease;
        }

        .project-card:active,
        .month-card:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 时间选择下拉菜单 -->
        <div class="dropdown">
            <!-- 标签页头部 -->
            <div class="dropdown-header">
                <div class="tabs-container">
                    <button class="tab active" id="tab-projects">指定项目</button>
                    <button class="tab" id="tab-timeline">最近沟通</button>
                </div>
            </div>

            <!-- 下拉内容 -->
            <div class="dropdown-content">
                <!-- 项目视图 -->
                <div class="projects-view" id="projects-view">
                    <h3>选择相关WorkFeed</h3>
                    <div class="projects-grid">
                        <div class="project-card">
                            <div class="project-name">项目名称1</div>
                            <div class="project-date">8月7日</div>
                        </div>
                        <div class="project-card">
                            <div class="project-name">项目名称1</div>
                            <div class="project-date">8月7日</div>
                        </div>
                        <div class="project-card">
                            <div class="project-name">项目名称1</div>
                            <div class="project-date">8月7日</div>
                        </div>
                        <div class="project-card">
                            <div class="project-name">项目名称1</div>
                            <div class="project-date">8月8日</div>
                        </div>
                        <div class="project-card">
                            <div class="project-name">项目名称1</div>
                            <div class="project-date">8月8日</div>
                        </div>
                        <div class="project-card">
                            <div class="project-name">项目名称1</div>
                            <div class="project-date">8月8日</div>
                        </div>
                    </div>
                </div>

                <!-- 时间线视图 -->
                <div class="timeline-view hidden" id="timeline-view">
                    <h3>您多久前沟通过？</h3>
                    <div class="time-options">
                        <button class="time-option">本周</button>
                        <button class="time-option active">上月</button>
                        <button class="time-option">近两周</button>
                    </div>
                    
                    <div class="timeline-question">您上次在几月份沟通过？</div>
                    
                    <div class="months-container">
                        <button class="nav-button" id="prev-months">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m15 18-6-6 6-6"/>
                            </svg>
                        </button>
                        
                        <div class="months-grid" id="months-grid">
                            <div class="month-card">
                                <svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>
                                <div class="month-name">3月</div>
                                <div class="month-year">2025</div>
                            </div>
                            <div class="month-card">
                                <svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>
                                <div class="month-name">4月</div>
                                <div class="month-year">2025</div>
                            </div>
                            <div class="month-card">
                                <svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>
                                <div class="month-name">5月</div>
                                <div class="month-year">2025</div>
                            </div>
                            <div class="month-card">
                                <svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>
                                <div class="month-name">6月</div>
                                <div class="month-year">2025</div>
                            </div>
                            <div class="month-card">
                                <svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>
                                <div class="month-name">7月</div>
                                <div class="month-year">2025</div>
                            </div>
                            <div class="month-card">
                                <svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: var(--linaria-theme_palette-foggy);"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>
                                <div class="month-name">8月</div>
                                <div class="month-year">2025</div>
                            </div>
                        </div>
                        
                        <button class="nav-button" id="next-months">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m9 18 6-6-6-6"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM 元素
        const tabProjects = document.getElementById('tab-projects');
        const tabTimeline = document.getElementById('tab-timeline');
        const projectsView = document.getElementById('projects-view');
        const timelineView = document.getElementById('timeline-view');
        const projectCards = document.querySelectorAll('.project-card');

        // 标签页切换
        tabProjects.addEventListener('click', function() {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            this.classList.add('active');
            projectsView.classList.remove('hidden');
            timelineView.classList.add('hidden');
        });

        tabTimeline.addEventListener('click', function() {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            this.classList.add('active');
            timelineView.classList.remove('hidden');
            projectsView.classList.add('hidden');
        });

        // Timeline Interaction Logic
        const timeOptions = document.querySelectorAll('.time-option');
        const monthCards = document.querySelectorAll('.month-card');
        const defaultIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';
        const selectedIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v16.07a2 2 0 0 1-.46 1.28l-.12.13L21 29.75a2 2 0 0 1-1.24.58H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h11.18v-5.66a5 5 0 0 1 4.78-5h5.88zm-.08 8h-5.58a3 3 0 0 0-3 2.82v5.76zm-18.58-16h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';

        function clearTimeOptions() {
            timeOptions.forEach(opt => opt.classList.remove('active'));
        }

        function clearMonthCards() {
            monthCards.forEach(c => {
                c.classList.remove('active');
                const icon = c.querySelector('.month-icon');
                if (icon) {
                    icon.innerHTML = defaultIconPath;
                    icon.style.fill = 'var(--linaria-theme_palette-foggy)';
                }
            });
        }

        function clearProjectCards() {
            projectCards.forEach(p => p.classList.remove('active'));
        }

        timeOptions.forEach(option => {
            option.addEventListener('click', function() {
                clearMonthCards();
                clearProjectCards();
                // Deselect all other options before selecting the current one
                timeOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
            });
        });

        monthCards.forEach(card => {
            card.addEventListener('click', function() {
                clearTimeOptions();
                clearProjectCards();
                
                this.classList.toggle('active');
                const icon = this.querySelector('.month-icon');

                if (this.classList.contains('active')) {
                    if (icon) {
                        icon.innerHTML = selectedIconPath;
                        icon.style.fill = 'var(--linaria-theme_palette-hof)';
                    }
                } else {
                    if (icon) {
                        icon.innerHTML = defaultIconPath;
                        icon.style.fill = 'var(--linaria-theme_palette-foggy)';
                    }
                }
            });
        });

        projectCards.forEach(card => {
            card.addEventListener('click', function() {
                clearTimeOptions();
                clearMonthCards();
                this.classList.toggle('active');
            });
        });

        const prevMonthsButton = document.getElementById('prev-months');
        const nextMonthsButton = document.getElementById('next-months');
        const monthsGrid = document.getElementById('months-grid');

        prevMonthsButton.addEventListener('click', () => {
            monthsGrid.scrollBy({ left: -200, behavior: 'smooth' });
        });

        nextMonthsButton.addEventListener('click', () => {
            monthsGrid.scrollBy({ left: 200, behavior: 'smooth' });
        });
    </script>
</body>
</html>