.dropdown-content .container {
    max-width: 780px;
    margin: 0 auto;
}

.dropdown-content .dropdown {
    background: white;
    border-radius: 16px;
    box-shadow: 0 6px 16px rgba(0,0,0,0.12);
    overflow: hidden;
    border: 1px solid #dddddd;
     overflow: visible;
}

.dropdown-content .dropdown-header {
    display: flex;
    justify-content: center;
}

.dropdown-content .tabs-container {
    display: flex;
    background: #ebebeb;
    border-radius: 32px;
    padding: 4px;
    gap: 5px;
}

.dropdown-content .tab {
    padding: 7px 22px;
    border-radius: 28px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    color: #666666;
    background: transparent;
    border: none;
    transition: all 0.2s ease;
    font-family: inherit;
    white-space: nowrap;
}

.dropdown-content .tab:hover {
    background: rgba(255, 255, 255, 0.5);
    color: #222;
}

.dropdown-content .tab.active {
    background: white;
    border: 1px solid #ddd;
    color: #222222;

}

.dropdown-content .tab.active:hover {
    background: white;
    color: #222222;
}

.dropdown-content .projects-view h3 {
    font-size: 18px;
    font-weight: 500;
    color: #222222;
    text-align: center;
    margin-bottom: 32px;
    line-height: 1.25;
}

.dropdown-content .projects-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
}

.dropdown-content .project-card {
    background: white;
    border: 1px solid #dddddd;
    border-radius: 12px;
    padding: 24px 34px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dropdown-content .project-card:hover {
    border-color: #222222;
}

.dropdown-content .project-card.active {
    background: #f7f7f7;
    border: 2px solid #222222;
}

.dropdown-content .project-name {
    font-weight: 500;
    color: #222222;
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 1.25;
}

.dropdown-content .project-date {
    color: #717171;
    font-size: 14px;
    line-height: 1.43;
}

.dropdown-content .timeline-view h3 {
    font-size: 18px;
    font-weight: 500;
    color: #222222;
    text-align: center;
    margin-bottom: 14px;
    line-height: 1.25;
}

.dropdown-content .time-options {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 48px;
}

.dropdown-content .time-option {
    padding: 9px 16px;
    border-radius: 32px;
    background: white;
    border: 1px solid #dddddd;
    cursor: pointer;
    transition: all 0.25s ease;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    font-family: inherit;
}

.dropdown-content .time-option:hover {
    border-color: #717171;
}

.dropdown-content .time-option.active {
    background: #f7f7f7;
    border: 2px solid #222222;
    color: #222222;
}

.dropdown-content .timeline-question {
    font-size: 18px;
    font-weight: 500;
    color: #222222;
    text-align: center;
    margin-bottom: 18px;
    line-height: 1.25;
}

.dropdown-content .months-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

/* .dropdown-content .nav-button {
    background: white;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    padding: 12px;
    border-radius: 50%;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: relative;
    z-index: 1;
} */

.dropdown-content .nav-button:hover {
    border-color: #cccccc;
    box-shadow: 0 0 0 1px transparent, 0 0 0 4px transparent, 0 6px 16px rgba(0,0,0,0.12);
}

.dropdown-content #prev-months {
    margin-right: -22px;
}

.dropdown-content #next-months {
    margin-left: -22px;
}

.dropdown-content .months-grid {
    display: flex;
    gap: 8px;
}



.dropdown-content .month-card:hover {
    border-color: #222222;
}

.dropdown-content .month-card.active {
    background: #f7f7f7;
    border: 2px solid #222222;
}

.dropdown-content .month-card.active:hover {
    background: #f7f7f7;
    border: 2px solid #222222;
}

.dropdown-content .month-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 12px;
    opacity: 0.7;
}

.dropdown-content .month-card.active .month-icon {
    opacity: 1;
}

.dropdown-content .month-name {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.25;
    color: #222222;
}

.dropdown-content .month-year {
    font-size: 0.75rem;
    line-height: 1.43;
    color: #222222;
}

.dropdown-content .hidden {
    display: none;
}

.dropdown-content .tab:active,
.dropdown-content .time-option:active,
.dropdown-content .nav-button:active {
    transform: scale(0.96);
    transition: transform 0.1s ease;
}

.dropdown-content .project-card:active,
.dropdown-content .month-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

